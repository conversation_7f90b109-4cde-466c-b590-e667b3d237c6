// Custom Call QA Analyzer - No OpenAI dependency
class CallQAAnalyzer {
    constructor() {
        // Initialize custom intent patterns and conversation flow rules
        this.initializeIntentPatterns();
    }

    initializeIntentPatterns() {
        // Enhanced Career Counseling Conversation Flow Patterns
        this.intentPatterns = {
            initial_greeting: {
                patterns: [
                    /\b(नमस्ते|hello|hi|good\s+(morning|afternoon|evening))\b/gi,
                    /\bमैं.*?(बोल\s+रही\s+हूँ|speaking|calling)\b/gi,
                    /\biDreamCareer\s+से\b/gi,
                    /\bcareer\s+counseling\b/gi,
                    /\bschool\s+में.*?counseling\b/gi
                ],
                score: 15,
                required: true,
                step: 1,
                description: "Initial greeting and introduction"
            },
            identity_verification: {
                patterns: [
                    /\bक्या\s+आप.*?हैं\b/gi,
                    /\bमैं.*?(हूँ|am)\b/gi,
                    /\b(papa|mama|father|mother|parent)\b/gi,
                    /\b(brother|sister|relative|family)\b/gi,
                    /\bवो\s+यहाँ\s+नहीं\s+हैं\b/gi,
                    /\bगलत\s+number\b/gi
                ],
                score: 12,
                required: true,
                step: 2,
                description: "Identity verification and relationship confirmation"
            },
            parent_response: {
                patterns: [
                    /\bधन्यवाद\b/gi,
                    /\bबच्चे\s+के\s+school\b/gi,
                    /\bcareer\s+counseling\s+provide\b/gi,
                    /\bbasic\s+information\s+collect\b/gi,
                    /\bconfirm\s+कर\s+सकते\s+हैं\b/gi
                ],
                score: 10,
                required: false,
                step: 3,
                description: "Parent engagement and information collection setup"
            },
            class_x_status: {
                patterns: [
                    /\bClass\s+Tenth\s+board\s+examination\b/gi,
                    /\bpass\s+कर\s+लिया\s+है\b/gi,
                    /\bpass\s+कया\s+है\b/gi,
                    /\bfail\b/gi,
                    /\bशायद|पता\s+नहीं\b/gi,
                    /\bresult\s+check\b/gi
                ],
                score: 15,
                required: true,
                step: 7,
                description: "Class X board exam status verification"
            },
            marks_percentage: {
                patterns: [
                    /\bकितने\s+percentage\b/gi,
                    /\bexact\s+percentage\b/gi,
                    /\b\d+\s*%\b/gi,
                    /\b(seventy|eighty|ninety|sixty|fifty)\s+percent\b/gi,
                    /\bmarks\s+मिले\s+थे\b/gi,
                    /\bकुल\s+कितने\s+marks\b/gi
                ],
                score: 12,
                required: true,
                step: 8,
                description: "Class X marks and percentage collection"
            },
            admission_status: {
                patterns: [
                    /\bClass\s+Eleventh\s+में\s+admission\b/gi,
                    /\bले\s+लिया\s+है\b/gi,
                    /\bITI|Polytechnic\b/gi,
                    /\bno\s+admission\b/gi,
                    /\bsame\s+school\b/gi,
                    /\bdifferent\s+school\b/gi
                ],
                score: 15,
                required: true,
                step: 10,
                description: "Class XI admission status verification"
            },
            institution_type: {
                patterns: [
                    /\bGovernment\s+है\b/gi,
                    /\bPrivate\b/gi,
                    /\bGovernment-aided\b/gi,
                    /\bकौन\s+से\s+type\s+का\s+school\b/gi,
                    /\binstitute\s+choose\b/gi
                ],
                score: 10,
                required: true,
                step: 11,
                description: "Institution type identification"
            },
            school_board_details: {
                patterns: [
                    /\bschool\s+का\s+क्या\s+name\b/gi,
                    /\bCentral\s+Board\s+of\s+Secondary\s+Education\b/gi,
                    /\bState\s+Board\b/gi,
                    /\bother\s+Board\b/gi,
                    /\baffiliated\s+है\b/gi,
                    /\bcertificate\s+पर\b/gi
                ],
                score: 10,
                required: true,
                step: 12,
                description: "School name and board affiliation details"
            },
            stream_selection: {
                patterns: [
                    /\bकौन\s+सा\s+stream\b/gi,
                    /\bScience\s+लया\s+है\b/gi,
                    /\bCommerce|Arts\b/gi,
                    /\bPCB|PCM\b/gi,
                    /\bPhysics.*?Chemistry.*?Biology\b/gi,
                    /\bPhysics.*?Chemistry.*?Mathematics\b/gi,
                    /\bMathematics\s+है\s+या\s+नहीं\b/gi
                ],
                score: 12,
                required: true,
                step: 13,
                description: "Stream selection and subject combination"
            },
            admission_proof: {
                patterns: [
                    /\bAdmission\s+proof\b/gi,
                    /\bID\s+card\b/gi,
                    /\badmission\s+receipt\b/gi,
                    /\bWhatsApp\s+account\b/gi,
                    /\bphoto\s+share\b/gi,
                    /\bdocuments\s+आराम\s+से\b/gi
                ],
                score: 8,
                required: false,
                step: 14,
                description: "Admission proof document collection"
            },
            dropout_investigation: {
                patterns: [
                    /\bspecific\s+reason\b/gi,
                    /\bपैसों\s+की\s+दिक्कत\b/gi,
                    /\bfees\s+की\s+वजह\b/gi,
                    /\bfamily\s+decisions\b/gi,
                    /\bhealth\s+issues\b/gi,
                    /\bacademic\s+struggles\b/gi,
                    /\bखास\s+वजह\b/gi
                ],
                score: 10,
                required: false,
                step: 15,
                description: "Dropout reason investigation"
            },
            summary_confirmation: {
                patterns: [
                    /\bsummarize\s+करती\s+हूँ\b/gi,
                    /\bStudent\s+का\s+नाम\b/gi,
                    /\bक्या\s+ये\s+सब\s+सही\s+है\b/gi,
                    /\bcorrection\s+करनी\s+है\b/gi,
                    /\bupdate\s+करो\b/gi
                ],
                score: 12,
                required: true,
                step: 16,
                description: "Information summary and confirmation"
            },
            closing_statement: {
                patterns: [
                    /\bआपका\s+time.*?धन्यवाद\b/gi,
                    /\bcounselor.*?contact\s+करेंगे\b/gi,
                    /\bproper\s+career\s+guidance\b/gi,
                    /\bआपका\s+दन\s+अच्छा\s+रहे\b/gi
                ],
                score: 10,
                required: true,
                step: 17,
                description: "Professional closing and next steps"
            },
            callback_scheduling: {
                patterns: [
                    /\bकौन\s+सा\s+time\s+convenient\b/gi,
                    /\bcall\s+back\s+के\s+लिए\b/gi,
                    /\bथोड़ी\s+देर\s+बाद\b/gi,
                    /\bदोबारा\s+try\s+करूँगी\b/gi,
                    /\bspecified\s+time\b/gi
                ],
                score: 8,
                required: false,
                step: 5,
                description: "Callback time scheduling"
            },
            goodbye: {
                patterns: [
                    /\bGoodbye\b/gi,
                    /\bधन्यवाद\b/gi,
                    /\bbye\b/gi,
                    /\btake\s+care\b/gi
                ],
                score: 5,
                required: true,
                step: 18,
                description: "Final goodbye"
            }
        };

        // Enhanced Career Counseling Conversation Flow
        this.expectedFlow = [
            'initial_greeting',
            'identity_verification',
            'class_x_status',
            'marks_percentage',
            'admission_status',
            'institution_type',
            'school_board_details',
            'stream_selection',
            'admission_proof',
            'summary_confirmation',
            'closing_statement',
            'goodbye'
        ];

        // Enhanced conversation step mapping with conditional logic
        this.conversationSteps = {
            1: { name: 'Initial Greeting', intent: 'initial_greeting', critical: true, always_required: true },
            2: { name: 'Identity Verification', intent: 'identity_verification', critical: true, always_required: true },
            3: { name: 'Parent Response', intent: 'parent_response', critical: false, conditional: 'parent_pickup' },
            4: { name: 'Busy Response', intent: 'callback_scheduling', critical: false, conditional: 'busy_response' },
            5: { name: 'Callback Scheduling', intent: 'callback_scheduling', critical: false, conditional: 'needs_callback' },
            6: { name: 'Student Response', intent: 'identity_verification', critical: true, conditional: 'student_pickup' },
            7: { name: 'Class X Board Exam Status', intent: 'class_x_status', critical: true, conditional: 'student_conversation' },
            8: { name: 'Class X Marks & Admission Status', intent: 'marks_percentage', critical: true, conditional: 'student_conversation' },
            9: { name: 'Post-Fail Plan', intent: 'dropout_investigation', critical: false, conditional: 'failed_class_x' },
            10: { name: 'School Change Confirmation', intent: 'admission_status', critical: true, conditional: 'passed_class_x' },
            11: { name: 'Institution Type', intent: 'institution_type', critical: true, conditional: 'has_admission' },
            12: { name: 'School & Board Details', intent: 'school_board_details', critical: true, conditional: 'has_admission' },
            13: { name: 'Stream Selection', intent: 'stream_selection', critical: true, conditional: 'has_admission' },
            14: { name: 'Admission Proof', intent: 'admission_proof', critical: false, conditional: 'has_admission' },
            15: { name: 'Dropout Reason Investigation', intent: 'dropout_investigation', critical: false, conditional: 'mentioned_dropout' },
            16: { name: 'Summary & Confirmation', intent: 'summary_confirmation', critical: true, conditional: 'successful_conversation' },
            17: { name: 'Closing Statement', intent: 'closing_statement', critical: true, conditional: 'successful_conversation' },
            18: { name: 'Goodbye', intent: 'goodbye', critical: true, always_required: true },
            19: { name: 'Voicemail Response', intent: 'initial_greeting', critical: false, conditional: 'voicemail' },
            20: { name: 'Wrong Number Handling', intent: 'goodbye', critical: false, conditional: 'wrong_number' }
        };

        // Define conversation flow contexts and their required steps
        this.conversationContexts = {
            student_direct: {
                name: 'Direct Student Conversation',
                required_steps: [1, 2, 6, 7, 8, 16, 17, 18],
                conditional_steps: {
                    passed_class_x: [10, 11, 12, 13, 14],
                    failed_class_x: [9],
                    mentioned_dropout: [15]
                }
            },
            parent_conversation: {
                name: 'Parent Conversation',
                required_steps: [1, 2, 3, 5, 18],
                conditional_steps: {}
            },
            busy_callback: {
                name: 'Busy/Callback Scenario',
                required_steps: [1, 2, 4, 18],
                conditional_steps: {}
            },
            wrong_number: {
                name: 'Wrong Number',
                required_steps: [1, 2, 20, 18],
                conditional_steps: {}
            },
            voicemail: {
                name: 'Voicemail',
                required_steps: [1, 19, 18],
                conditional_steps: {}
            }
        };

        // Enhanced satisfaction indicators with Hindi support
        this.satisfactionPatterns = {
            positive: [
                /\b(धन्यवाद|thank\s+you|thanks|great|perfect|excellent|wonderful)\b/gi,
                /\b(बहुत\s+अच्छा|very\s+good|that\s+works|that's\s+perfect)\b/gi,
                /\b(helpful|really\s+appreciate|समझ\s+गई)\b/gi,
                /\b(ठीक\s+है|okay|alright|fine)\b/gi
            ],
            negative: [
                /\b(परेशान|frustrated|annoyed|disappointed|unhappy|terrible)\b/gi,
                /\b(समस्या|problem|issue|waste\s+of\s+time|not\s+helpful)\b/gi,
                /\b(गलत|wrong|incorrect|manager|escalate)\b/gi,
                /\b(व्यस्त|busy|not\s+interested)\b/gi
            ],
            neutral: [
                /\b(शायद|maybe|perhaps|पता\s+नहीं|don't\s+know)\b/gi,
                /\b(देखते\s+हैं|let's\s+see|we'll\s+see)\b/gi
            ]
        };
    }

    async analyzeCall(transcript, audioData, config) {
        console.log('🚀 Starting comprehensive call analysis...');
        
        try {
            // Perform all analyses
            const silenceSegments = this.detectSilenceSegments(audioData, config);
            const repetitions = this.detectRepetitions(transcript, config);
            const latencyAnalysis = this.analyzeCallLatency(audioData, config);
            const intentFlow = await this.detectIntents(transcript);
            
            const { overallScore, scoreBreakdown } = this.calculateWeightedScore(
                silenceSegments, repetitions, latencyAnalysis, intentFlow
            );
            
            const visualizationData = this.generateVisualizationData(audioData, silenceSegments);
            
            console.log('✅ Analysis completed successfully!');
            
            return {
                overallScore,
                callDuration: audioData ? audioData.duration : 0,
                silenceViolations: silenceSegments,
                repetitions,
                intentFlow,
                latencyAnalysis,
                scoreBreakdown,
                visualizationData
            };
            
        } catch (error) {
            console.error('❌ Error during analysis:', error);
            throw error;
        }
    }

    detectSilenceSegments(audioData, config) {
        console.log('🔇 Detecting silence segments...');
        
        if (!audioData) {
            // Create mock silence segments for demonstration
            return [
                {
                    startTime: 45.2,
                    endTime: 51.8,
                    duration: 6.6,
                    speaker: 'bot'
                },
                {
                    startTime: 89.1,
                    endTime: 95.3,
                    duration: 6.2,
                    speaker: 'bot'
                }
            ];
        }

        const silenceSegments = [];
        const threshold = config.silenceThreshold;
        
        // Simple silence detection logic
        // In a real implementation, you'd use audio processing libraries
        if (audioData.silences) {
            audioData.silences.forEach((silence, index) => {
                if (silence.duration >= threshold) {
                    silenceSegments.push({
                        startTime: silence.start,
                        endTime: silence.end,
                        duration: silence.duration,
                        speaker: index % 2 === 0 ? 'bot' : 'human'
                    });
                }
            });
        }

        console.log(`✅ Found ${silenceSegments.length} silence segments`);
        return silenceSegments;
    }

    detectRepetitions(transcript, config) {
        console.log('🔄 Detecting repetitions...');
        
        const repetitions = [];
        const botTurns = this.extractBotTurns(transcript);
        
        for (let i = 0; i < botTurns.length; i++) {
            for (let j = i + 1; j < botTurns.length; j++) {
                const similarity = this.calculateSimilarity(botTurns[i].text, botTurns[j].text);
                
                if (similarity > config.repetitionSimilarityThreshold) {
                    repetitions.push({
                        turn1: botTurns[i].turnNumber,
                        turn2: botTurns[j].turnNumber,
                        similarityScore: similarity,
                        text1: botTurns[i].text,
                        text2: botTurns[j].text
                    });
                }
            }
        }
        
        console.log(`✅ Found ${repetitions.length} repetitions`);
        return repetitions;
    }

    extractBotTurns(transcript) {
        const botTurns = [];
        const lines = transcript.split('\n');
        let turnNumber = 0;
        
        lines.forEach(line => {
            line = line.trim();
            if (line.startsWith('Chat Bot:') || line.startsWith('Bot:')) {
                turnNumber++;
                const text = line.includes(':') ? line.split(':', 2)[1].trim() : line;
                botTurns.push({
                    turnNumber,
                    text
                });
            }
        });
        
        return botTurns;
    }

    calculateSimilarity(text1, text2) {
        // Simple similarity calculation using Levenshtein distance
        const matrix = [];
        const len1 = text1.length;
        const len2 = text2.length;

        for (let i = 0; i <= len2; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= len1; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= len2; i++) {
            for (let j = 1; j <= len1; j++) {
                if (text2.charAt(i - 1) === text1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        const maxLen = Math.max(len1, len2);
        return maxLen === 0 ? 1 : (maxLen - matrix[len2][len1]) / maxLen;
    }

    analyzeCallLatency(audioData, config) {
        console.log('⏱️ Analyzing call latency...');
        
        const totalDurationMinutes = audioData ? audioData.duration / 60 : 3.0; // Default 3 minutes
        const idealMin = config.idealCallDurationMin;
        const idealMax = config.idealCallDurationMax;
        
        const latencyAnalysis = {
            totalDurationMinutes,
            idealRangeMin: idealMin,
            idealRangeMax: idealMax,
            withinIdealRange: totalDurationMinutes >= idealMin && totalDurationMinutes <= idealMax,
            deviationFromIdeal: 0
        };
        
        if (totalDurationMinutes < idealMin) {
            latencyAnalysis.deviationFromIdeal = idealMin - totalDurationMinutes;
            latencyAnalysis.status = 'too_short';
        } else if (totalDurationMinutes > idealMax) {
            latencyAnalysis.deviationFromIdeal = totalDurationMinutes - idealMax;
            latencyAnalysis.status = 'too_long';
        } else {
            latencyAnalysis.status = 'optimal';
        }
        
        console.log(`✅ Call duration: ${totalDurationMinutes.toFixed(1)} minutes (${latencyAnalysis.status})`);
        return latencyAnalysis;
    }

    async detectIntents(transcript) {
        console.log('🎭 Detecting intents using enhanced career counseling logic...');

        const turns = this.parseTranscriptTurns(transcript);
        const intentMappings = [];
        const detectedIntents = new Set();
        const stepProgression = [];
        let totalConfidence = 0;

        // Process each turn with enhanced intent detection
        for (let i = 0; i < turns.length; i++) {
            const turn = turns[i];
            const { intent, step, confidence, stepNumber } = this.enhancedIntentDetection(turn.text, turn.speaker, i, turns);

            const mapping = {
                turnNumber: i + 1,
                speaker: turn.speaker,
                text: turn.text.substring(0, 100) + (turn.text.length > 100 ? '...' : ''), // Truncate for display
                detectedIntent: intent,
                confidence: Math.round(confidence * 100) / 100, // Round to 2 decimal places
                conversationStep: step,
                stepNumber: stepNumber
            };

            intentMappings.push(mapping);
            totalConfidence += confidence;

            // Track detected intents and step progression
            if (step !== 'unknown') {
                detectedIntents.add(step);
                stepProgression.push({
                    step: stepNumber,
                    intent: step,
                    confidence: confidence,
                    turnNumber: i + 1
                });
            }
        }

        // Determine conversation context and calculate contextual scoring
        const conversationContext = this.determineConversationContext(intentMappings);
        const contextualAnalysis = this.calculateContextualFlowScore(conversationContext, stepProgression, intentMappings);

        // Calculate priority-based weighted average confidence
        const averageConfidence = this.calculatePriorityBasedConfidence(intentMappings, conversationContext);

        console.log(`✅ Detected intents for ${intentMappings.length} turns`);
        console.log(`🎯 Conversation context: ${conversationContext.name}`);
        console.log(`📊 Contextual flow score: ${contextualAnalysis.flowScore.toFixed(1)}/100`);
        console.log(`🎯 Average intent confidence: ${(averageConfidence * 100).toFixed(1)}%`);
        console.log(`🔍 Completed steps: ${contextualAnalysis.completedRequiredSteps}/${contextualAnalysis.totalRequiredSteps}`);
        console.log(`⚠️ Missing critical steps: ${contextualAnalysis.missingCriticalSteps.length}`);

        return {
            intentMappings,
            flowScore: Math.max(0, Math.min(100, contextualAnalysis.flowScore)),
            averageConfidence: averageConfidence,
            completedSteps: contextualAnalysis.completedRequiredSteps,
            totalRequiredSteps: contextualAnalysis.totalRequiredSteps,
            detectedIntents: Array.from(detectedIntents),
            stepProgression,
            missingCriticalSteps: contextualAnalysis.missingCriticalSteps,
            conversationContext: conversationContext,
            contextualAnalysis: contextualAnalysis,
            conversationQuality: this.assessEnhancedConversationQuality(contextualAnalysis, averageConfidence)
        };
    }

    enhancedIntentDetection(text, speaker, turnIndex, allTurns) {
        const textLower = text.toLowerCase();
        let bestMatch = {
            intent: 'General conversation',
            step: 'unknown',
            confidence: 0.2,
            stepNumber: 0
        };

        // Enhanced pattern matching with weighted scoring
        for (const [intentType, intentData] of Object.entries(this.intentPatterns)) {
            let matchScore = 0;
            let patternMatches = 0;
            const totalPatterns = intentData.patterns.length;

            // Check each pattern and calculate weighted score
            for (const pattern of intentData.patterns) {
                if (pattern.test(text)) {
                    patternMatches++;
                    // Give higher weight to more specific patterns
                    matchScore += intentData.score || 10;
                }
            }

            // Calculate confidence based on pattern matches and context
            let confidence = patternMatches / totalPatterns;

            // Boost confidence for exact matches
            if (patternMatches === totalPatterns) {
                confidence = Math.min(0.95, confidence + 0.2);
            } else if (patternMatches > 0) {
                confidence = Math.min(0.85, confidence + 0.1);
            }

            // Context-aware confidence adjustment
            confidence = this.adjustConfidenceWithContext(confidence, intentType, speaker, turnIndex, allTurns, textLower);

            if (confidence > bestMatch.confidence) {
                bestMatch = {
                    intent: this.getEnhancedIntentDescription(intentType, text),
                    step: intentType,
                    confidence: confidence,
                    stepNumber: intentData.step || 0
                };
            }
        }

        // Apply speaker-specific adjustments
        bestMatch = this.applySpeakerSpecificAdjustments(bestMatch, speaker, text);

        return bestMatch;
    }

    getEnhancedIntentDescription(intentType, text) {
        const descriptions = {
            initial_greeting: 'Initial greeting and agent introduction',
            identity_verification: 'Student/parent identity verification',
            parent_response: 'Parent engagement and information setup',
            class_x_status: 'Class X board exam status inquiry',
            marks_percentage: 'Class X marks and percentage collection',
            admission_status: 'Class XI admission status verification',
            institution_type: 'School/institution type identification',
            school_board_details: 'School name and board affiliation',
            stream_selection: 'Academic stream and subject selection',
            admission_proof: 'Admission document collection',
            dropout_investigation: 'Dropout reason investigation',
            summary_confirmation: 'Information summary and confirmation',
            closing_statement: 'Professional closing and next steps',
            callback_scheduling: 'Callback appointment scheduling',
            goodbye: 'Final farewell and call termination'
        };
        return descriptions[intentType] || 'General conversation';
    }

    adjustConfidenceWithContext(confidence, intentType, speaker, turnIndex, allTurns, textLower) {
        // Boost confidence for agent-specific intents when speaker is agent
        if (speaker.toLowerCase().includes('bot') || speaker.toLowerCase().includes('agent')) {
            const agentIntents = ['initial_greeting', 'class_x_status', 'marks_percentage', 'admission_status',
                                'institution_type', 'school_board_details', 'stream_selection', 'closing_statement'];
            if (agentIntents.includes(intentType)) {
                confidence = Math.min(0.95, confidence + 0.15);
            }
        }

        // Boost confidence for student/parent responses when speaker is human
        if (speaker.toLowerCase().includes('human') || speaker.toLowerCase().includes('student')) {
            const responseIntents = ['identity_verification', 'parent_response'];
            if (responseIntents.includes(intentType)) {
                confidence = Math.min(0.90, confidence + 0.1);
            }
        }

        // Sequential flow bonus - if this intent logically follows previous intents
        if (turnIndex > 0) {
            const previousIntent = this.getPreviousIntent(turnIndex, allTurns);
            if (this.isLogicalSequence(previousIntent, intentType)) {
                confidence = Math.min(0.95, confidence + 0.1);
            }
        }

        // Penalize very short responses for complex intents
        if (textLower.length < 10 && ['marks_percentage', 'school_board_details', 'stream_selection'].includes(intentType)) {
            confidence = Math.max(0.1, confidence - 0.2);
        }

        return confidence;
    }

    applySpeakerSpecificAdjustments(bestMatch, speaker, text) {
        // Apply final adjustments based on speaker role
        if (speaker.toLowerCase().includes('bot') || speaker.toLowerCase().includes('agent')) {
            // Agent should have higher confidence for structured questions
            if (['class_x_status', 'marks_percentage', 'admission_status'].includes(bestMatch.step)) {
                bestMatch.confidence = Math.min(0.95, bestMatch.confidence + 0.1);
            }
        }

        // Ensure minimum confidence for any detected pattern
        if (bestMatch.confidence < 0.3 && bestMatch.step !== 'unknown') {
            bestMatch.confidence = 0.3;
        }

        return bestMatch;
    }

    getPreviousIntent(turnIndex, allTurns) {
        // Simple implementation - could be enhanced with more sophisticated logic
        return turnIndex > 0 ? 'previous_intent' : null;
    }

    isLogicalSequence(previousIntent, currentIntent) {
        // Define logical conversation flow sequences
        const logicalSequences = {
            'initial_greeting': ['identity_verification'],
            'identity_verification': ['parent_response', 'class_x_status'],
            'class_x_status': ['marks_percentage'],
            'marks_percentage': ['admission_status'],
            'admission_status': ['institution_type'],
            'institution_type': ['school_board_details'],
            'school_board_details': ['stream_selection'],
            'stream_selection': ['admission_proof', 'summary_confirmation']
        };

        return logicalSequences[previousIntent]?.includes(currentIntent) || false;
    }

    calculatePriorityBasedConfidence(intentMappings, conversationContext) {
        if (intentMappings.length === 0) return 0;

        // Define critical steps for the detected context
        const contextRequiredSteps = conversationContext.required_steps || [];
        const criticalStepIntents = contextRequiredSteps
            .map(stepNum => this.conversationSteps[stepNum])
            .filter(step => step && step.critical)
            .map(step => step.intent);

        // Categorize mappings by priority and quality
        const highConfidenceMappings = intentMappings.filter(m => m.confidence > 0.7);
        const mediumConfidenceMappings = intentMappings.filter(m => m.confidence >= 0.4 && m.confidence <= 0.7);
        const lowConfidenceMappings = intentMappings.filter(m => m.confidence < 0.4);

        const criticalStepMappings = intentMappings.filter(m =>
            criticalStepIntents.includes(m.conversationStep)
        );

        const vagueMappings = this.identifyVagueMessages(intentMappings);
        const clearMappings = intentMappings.filter(m => !vagueMappings.includes(m));

        // Calculate weighted confidence with priority system
        let totalWeightedConfidence = 0;
        let totalWeight = 0;

        // Priority 1: High confidence critical steps (weight: 4.0)
        const highConfCritical = criticalStepMappings.filter(m => m.confidence > 0.7);
        highConfCritical.forEach(mapping => {
            totalWeightedConfidence += mapping.confidence * 4.0;
            totalWeight += 4.0;
        });

        // Priority 2: Medium confidence critical steps (weight: 3.0)
        const mediumConfCritical = criticalStepMappings.filter(m =>
            m.confidence >= 0.4 && m.confidence <= 0.7
        );
        mediumConfCritical.forEach(mapping => {
            totalWeightedConfidence += mapping.confidence * 3.0;
            totalWeight += 3.0;
        });

        // Priority 3: High confidence non-critical clear steps (weight: 2.5)
        const highConfNonCriticalClear = clearMappings.filter(m =>
            m.confidence > 0.7 && !criticalStepIntents.includes(m.conversationStep)
        );
        highConfNonCriticalClear.forEach(mapping => {
            totalWeightedConfidence += mapping.confidence * 2.5;
            totalWeight += 2.5;
        });

        // Priority 4: Medium confidence non-critical clear steps (weight: 2.0)
        const mediumConfNonCriticalClear = clearMappings.filter(m =>
            m.confidence >= 0.4 && m.confidence <= 0.7 &&
            !criticalStepIntents.includes(m.conversationStep)
        );
        mediumConfNonCriticalClear.forEach(mapping => {
            totalWeightedConfidence += mapping.confidence * 2.0;
            totalWeight += 2.0;
        });

        // Priority 5: Low confidence critical steps (weight: 1.5)
        const lowConfCritical = criticalStepMappings.filter(m => m.confidence < 0.4);
        lowConfCritical.forEach(mapping => {
            totalWeightedConfidence += mapping.confidence * 1.5;
            totalWeight += 1.5;
        });

        // Priority 6: Vague messages (weight: 0.5)
        vagueMappings.forEach(mapping => {
            totalWeightedConfidence += mapping.confidence * 0.5;
            totalWeight += 0.5;
        });

        // Priority 7: Low confidence non-critical steps (weight: 1.0)
        const lowConfNonCritical = intentMappings.filter(m =>
            m.confidence < 0.4 &&
            !criticalStepIntents.includes(m.conversationStep) &&
            !vagueMappings.includes(m)
        );
        lowConfNonCritical.forEach(mapping => {
            totalWeightedConfidence += mapping.confidence * 1.0;
            totalWeight += 1.0;
        });

        const priorityBasedConfidence = totalWeight > 0 ? totalWeightedConfidence / totalWeight : 0;

        // Calculate confidence quality metrics
        const confidenceMetrics = {
            priorityBasedAverage: priorityBasedConfidence,
            simpleAverage: intentMappings.reduce((sum, m) => sum + m.confidence, 0) / intentMappings.length,
            highConfidenceCount: highConfidenceMappings.length,
            criticalStepsCount: criticalStepMappings.length,
            vagueMessagesCount: vagueMappings.length,
            clearMessagesCount: clearMappings.length,
            confidenceDistribution: {
                high: highConfidenceMappings.length,
                medium: mediumConfidenceMappings.length,
                low: lowConfidenceMappings.length
            }
        };

        console.log(`🎯 Priority-based confidence: ${(priorityBasedConfidence * 100).toFixed(1)}%`);
        console.log(`📊 Simple average: ${(confidenceMetrics.simpleAverage * 100).toFixed(1)}%`);
        console.log(`🔍 High confidence steps: ${confidenceMetrics.highConfidenceCount}`);
        console.log(`⭐ Critical steps: ${confidenceMetrics.criticalStepsCount}`);
        console.log(`💬 Vague messages: ${confidenceMetrics.vagueMessagesCount}`);

        return priorityBasedConfidence;
    }

    identifyVagueMessages(intentMappings) {
        const vagueMessages = [];

        intentMappings.forEach(mapping => {
            const text = mapping.text.toLowerCase();
            const isVague = this.isMessageVague(text, mapping.speaker);

            if (isVague) {
                vagueMessages.push(mapping);
            }
        });

        return vagueMessages;
    }

    isMessageVague(text, speaker) {
        // Define vague patterns for both bot and human
        const vaguePatterns = {
            bot: [
                /\b(okay|ok|ठीक है|समझ गई)\b/gi,
                /\b(हाँ|yes|हम्म|hmm)\b/gi,
                /\b(और कुछ|anything else|कोई और)\b/gi,
                /\b(देखते हैं|let's see|पता नहीं)\b/gi
            ],
            human: [
                /\b(हाँ|yes|ठीक है|okay|ok)\b/gi,
                /\b(नहीं|no|ना)\b/gi,
                /\b(पता नहीं|don't know|मालूम नहीं)\b/gi,
                /\b(शायद|maybe|हो सकता है)\b/gi,
                /\b(हम्म|hmm|उम्म|umm)\b/gi
            ]
        };

        // Check if message is very short (likely vague)
        if (text.length < 10) {
            return true;
        }

        // Check for vague patterns based on speaker
        const speakerType = (speaker.toLowerCase().includes('bot') || speaker.toLowerCase().includes('agent')) ? 'bot' : 'human';
        const patterns = vaguePatterns[speakerType];

        // If message consists mostly of vague patterns
        let vagueMatches = 0;
        patterns.forEach(pattern => {
            if (pattern.test(text)) {
                vagueMatches++;
            }
        });

        // Consider vague if multiple vague patterns match or if it's a very short response
        return vagueMatches >= 2 || (vagueMatches >= 1 && text.length < 20);
    }

    determineConversationContext(intentMappings) {
        // Analyze the conversation to determine the primary context
        const detectedIntents = intentMappings.map(m => m.conversationStep);
        const detectedSteps = intentMappings.map(m => m.stepNumber).filter(s => s > 0);

        // Check for specific conversation patterns
        const hasParentResponse = detectedIntents.includes('parent_response');
        const hasBusyResponse = detectedIntents.includes('callback_scheduling') &&
                               intentMappings.some(m => m.text.toLowerCase().includes('busy') ||
                                                      m.text.toLowerCase().includes('व्यस्त'));
        const hasWrongNumber = detectedIntents.includes('goodbye') &&
                              intentMappings.some(m => m.text.toLowerCase().includes('wrong') ||
                                                     m.text.toLowerCase().includes('गलत'));
        const hasVoicemail = detectedIntents.includes('initial_greeting') &&
                            intentMappings.some(m => m.text.toLowerCase().includes('voicemail') ||
                                                   m.text.toLowerCase().includes('message'));
        const hasStudentConversation = detectedIntents.includes('class_x_status') ||
                                      detectedIntents.includes('marks_percentage');

        // Determine primary context
        if (hasWrongNumber) {
            return this.conversationContexts.wrong_number;
        } else if (hasVoicemail) {
            return this.conversationContexts.voicemail;
        } else if (hasBusyResponse && !hasStudentConversation) {
            return this.conversationContexts.busy_callback;
        } else if (hasParentResponse && !hasStudentConversation) {
            return this.conversationContexts.parent_conversation;
        } else if (hasStudentConversation) {
            return this.conversationContexts.student_direct;
        } else {
            // Default to student direct if unclear
            return this.conversationContexts.student_direct;
        }
    }

    calculateContextualFlowScore(conversationContext, stepProgression, intentMappings) {
        let flowScore = 0;
        const maxScore = 100;

        // Get required steps for this context
        const requiredSteps = conversationContext.required_steps;
        const completedSteps = stepProgression.map(p => p.step);
        const completedRequiredSteps = requiredSteps.filter(step => completedSteps.includes(step));

        // Base score for context identification (20 points)
        flowScore += 20;

        // Required steps completion (40 points)
        const requiredStepsScore = (completedRequiredSteps.length / requiredSteps.length) * 40;
        flowScore += requiredStepsScore;

        // Conditional steps handling (20 points)
        let conditionalScore = 0;
        const conversationContent = intentMappings.map(m => m.text.toLowerCase()).join(' ');

        // Check for conditional triggers and their completion
        if (conversationContext.conditional_steps) {
            let applicableConditionalSteps = 0;
            let completedConditionalSteps = 0;

            // Check each conditional category
            for (const [condition, steps] of Object.entries(conversationContext.conditional_steps)) {
                if (this.isConditionMet(condition, conversationContent, intentMappings)) {
                    applicableConditionalSteps += steps.length;
                    const completedInCategory = steps.filter(step => completedSteps.includes(step)).length;
                    completedConditionalSteps += completedInCategory;
                }
            }

            if (applicableConditionalSteps > 0) {
                conditionalScore = (completedConditionalSteps / applicableConditionalSteps) * 20;
            } else {
                conditionalScore = 20; // Full points if no conditionals apply
            }
        } else {
            conditionalScore = 20; // Full points if no conditionals defined
        }
        flowScore += conditionalScore;

        // High confidence bonus (10 points)
        const highConfidenceSteps = stepProgression.filter(p => p.confidence > 0.7).length;
        const confidenceBonus = stepProgression.length > 0 ?
            (highConfidenceSteps / stepProgression.length) * 10 : 0;
        flowScore += confidenceBonus;

        // Sequential flow bonus (10 points)
        let sequentialBonus = 0;
        for (let i = 1; i < stepProgression.length; i++) {
            if (stepProgression[i].step >= stepProgression[i-1].step) {
                sequentialBonus += 1;
            }
        }
        if (stepProgression.length > 1) {
            sequentialBonus = (sequentialBonus / (stepProgression.length - 1)) * 10;
        }
        flowScore += sequentialBonus;

        // Identify missing critical steps for this context
        const criticalRequiredSteps = requiredSteps.filter(stepNum => {
            const stepInfo = this.conversationSteps[stepNum];
            return stepInfo && stepInfo.critical;
        });
        const missingCriticalSteps = criticalRequiredSteps
            .filter(step => !completedSteps.includes(step))
            .map(step => this.conversationSteps[step].intent);

        return {
            flowScore: Math.min(maxScore, Math.max(0, flowScore)),
            completedRequiredSteps: completedRequiredSteps.length,
            totalRequiredSteps: requiredSteps.length,
            missingCriticalSteps,
            contextName: conversationContext.name,
            requiredStepsScore,
            conditionalScore,
            confidenceBonus,
            sequentialBonus
        };
    }

    isConditionMet(condition, conversationContent, intentMappings) {
        switch (condition) {
            case 'passed_class_x':
                return conversationContent.includes('pass') ||
                       conversationContent.includes('पास') ||
                       intentMappings.some(m => m.conversationStep === 'marks_percentage');

            case 'failed_class_x':
                return conversationContent.includes('fail') ||
                       conversationContent.includes('फेल') ||
                       conversationContent.includes('नहीं पास');

            case 'mentioned_dropout':
                return conversationContent.includes('dropout') ||
                       conversationContent.includes('छोड़') ||
                       conversationContent.includes('बंद कर');

            case 'has_admission':
                return conversationContent.includes('admission') ||
                       conversationContent.includes('एडमिशन') ||
                       intentMappings.some(m => m.conversationStep === 'admission_status');

            default:
                return false;
        }
    }

    assessEnhancedConversationQuality(contextualAnalysis, averageConfidence) {
        let qualityScore = 0;

        // Context-appropriate completion (40 points)
        const completionRate = contextualAnalysis.completedRequiredSteps / contextualAnalysis.totalRequiredSteps;
        qualityScore += completionRate * 40;

        // Average confidence (30 points)
        qualityScore += averageConfidence * 30;

        // Missing critical steps penalty (20 points)
        const criticalStepsPenalty = contextualAnalysis.missingCriticalSteps.length * 5;
        qualityScore += Math.max(0, 20 - criticalStepsPenalty);

        // Flow coherence (10 points)
        qualityScore += (contextualAnalysis.sequentialBonus / 10) * 10;

        // Determine quality rating
        let rating = 'Poor';
        if (qualityScore >= 85) rating = 'Excellent';
        else if (qualityScore >= 70) rating = 'Good';
        else if (qualityScore >= 50) rating = 'Fair';

        return {
            rating,
            score: Math.round(qualityScore),
            factors: {
                contextAppropriate: completionRate > 0.8,
                goodConfidence: averageConfidence > 0.6,
                excellentConfidence: averageConfidence > 0.8,
                noCriticalMissing: contextualAnalysis.missingCriticalSteps.length === 0,
                goodFlow: contextualAnalysis.sequentialBonus > 5
            },
            breakdown: {
                completionScore: Math.round(completionRate * 40),
                confidenceScore: Math.round(averageConfidence * 30),
                criticalStepsScore: Math.round(Math.max(0, 20 - criticalStepsPenalty)),
                flowScore: Math.round((contextualAnalysis.sequentialBonus / 10) * 10)
            }
        };
    }

    calculateEnhancedFlowScore(stepProgression, intentMappings) {
        let flowScore = 0;
        const maxScore = 100;

        // Base score for having any conversation flow
        if (stepProgression.length > 0) {
            flowScore += 20;
        }

        // Sequential flow scoring
        let sequentialBonus = 0;
        for (let i = 1; i < stepProgression.length; i++) {
            const currentStep = stepProgression[i].step;
            const previousStep = stepProgression[i - 1].step;

            // Award points for logical progression
            if (currentStep > previousStep) {
                sequentialBonus += 5;
            } else if (currentStep === previousStep + 1) {
                sequentialBonus += 10; // Perfect sequence
            }
        }
        flowScore += Math.min(30, sequentialBonus);

        // Critical steps completion bonus
        const criticalSteps = Object.values(this.conversationSteps)
            .filter(step => step.critical)
            .map(step => step.intent);

        const completedCriticalSteps = stepProgression
            .map(p => p.intent)
            .filter(intent => criticalSteps.includes(intent));

        const criticalCompletionRate = completedCriticalSteps.length / criticalSteps.length;
        flowScore += criticalCompletionRate * 25;

        // High confidence bonus
        const highConfidenceSteps = stepProgression.filter(p => p.confidence > 0.7);
        const confidenceBonus = (highConfidenceSteps.length / stepProgression.length) * 15;
        flowScore += confidenceBonus;

        // Conversation completeness bonus
        const totalSteps = Object.keys(this.conversationSteps).length;
        const completionRate = stepProgression.length / totalSteps;
        flowScore += completionRate * 10;

        return Math.min(maxScore, Math.max(0, flowScore));
    }

    assessConversationQuality(stepProgression, averageConfidence, missingCriticalSteps) {
        let quality = 'Poor';
        let score = 0;

        // Calculate quality score
        if (stepProgression.length > 0) score += 20;
        if (averageConfidence > 0.6) score += 25;
        if (averageConfidence > 0.8) score += 15;
        if (missingCriticalSteps.length === 0) score += 25;
        if (stepProgression.length >= 8) score += 15; // Good conversation length

        // Determine quality rating
        if (score >= 80) quality = 'Excellent';
        else if (score >= 65) quality = 'Good';
        else if (score >= 45) quality = 'Fair';
        else quality = 'Poor';

        return {
            rating: quality,
            score: score,
            factors: {
                hasConversationFlow: stepProgression.length > 0,
                goodConfidence: averageConfidence > 0.6,
                excellentConfidence: averageConfidence > 0.8,
                allCriticalStepsPresent: missingCriticalSteps.length === 0,
                adequateLength: stepProgression.length >= 8
            }
        };
    }

    // Legacy method - kept for compatibility but enhanced
    adjustIntentWithContext(match, speaker, turnIndex, allTurns, textLower) {
        // This method is now handled by the enhanced detection logic
        // Keeping for backward compatibility
        return match;
    }

    analyzeSatisfaction(textLower) {
        for (const pattern of this.satisfactionPatterns.positive) {
            if (pattern.test(textLower)) {
                return 'positive';
            }
        }

        for (const pattern of this.satisfactionPatterns.negative) {
            if (pattern.test(textLower)) {
                return 'negative';
            }
        }

        return 'neutral';
    }



    parseTranscriptTurns(transcript) {
        const turns = [];
        const lines = transcript.split('\n');

        lines.forEach(line => {
            line = line.trim();
            if (!line) return;

            let speaker, text;

            // Enhanced speaker detection patterns
            if (line.match(/^(Chat Bot|Bot|Agent|Support|Assistant):/i)) {
                speaker = 'agent';
                text = line.includes(':') ? line.split(':', 2)[1].trim() : line;
            } else if (line.match(/^(Human|User|Customer|Client|Caller):/i)) {
                speaker = 'customer';
                text = line.includes(':') ? line.split(':', 2)[1].trim() : line;
            } else if (line.includes(':')) {
                // Generic speaker:text format
                const parts = line.split(':', 2);
                speaker = parts[0].trim().toLowerCase().includes('bot') ||
                         parts[0].trim().toLowerCase().includes('agent') ? 'agent' : 'customer';
                text = parts[1].trim();
            } else {
                return; // Skip lines without clear speaker identification
            }

            if (text && text.length > 0) {
                turns.push({ speaker, text });
            }
        });

        return turns;
    }

    calculateWeightedScore(silenceSegments, repetitions, latencyAnalysis, intentFlow) {
        console.log('📊 Calculating weighted scores...');

        const weights = {
            silenceCompliance: 0.25,
            repetitionAvoidance: 0.20,
            latencyOptimization: 0.25,
            intentFlowAccuracy: 0.30
        };
        
        const scores = {};
        
        // Silence score
        const silenceViolations = silenceSegments.length;
        const maxExpectedViolations = 5;
        scores.silenceCompliance = Math.max(0, 100 - (silenceViolations / maxExpectedViolations) * 100);
        
        // Repetition score
        const repetitionCount = repetitions.length;
        const maxExpectedRepetitions = 3;
        scores.repetitionAvoidance = Math.max(0, 100 - (repetitionCount / maxExpectedRepetitions) * 100);
        
        // Latency score
        if (latencyAnalysis.withinIdealRange) {
            scores.latencyOptimization = 100;
        } else {
            const deviation = latencyAnalysis.deviationFromIdeal;
            const maxAcceptableDeviation = 2.0;
            scores.latencyOptimization = Math.max(0, 100 - (deviation / maxAcceptableDeviation) * 100);
        }
        
        // Enhanced Intent flow score calculation
        if (intentFlow && intentFlow.flowScore !== undefined) {
            scores.intentFlowAccuracy = intentFlow.flowScore;
        } else {
            scores.intentFlowAccuracy = 30; // Lower default for missing intent data
        }
        
        const overallScore = Object.keys(weights).reduce((sum, metric) => {
            return sum + (scores[metric] * weights[metric]);
        }, 0);
        
        const scoreBreakdown = {
            overallScore,
            componentScores: scores,
            weights,
            explanations: {
                silenceCompliance: `Found ${silenceViolations} silence violations (threshold: ${maxExpectedViolations})`,
                repetitionAvoidance: `Found ${repetitionCount} repetitions (threshold: ${maxExpectedRepetitions})`,
                latencyOptimization: `Call duration: ${latencyAnalysis.totalDurationMinutes.toFixed(1)}min (ideal: ${latencyAnalysis.idealRangeMin}-${latencyAnalysis.idealRangeMax}min)`,
                intentFlowAccuracy: `Flow score: ${intentFlow.flowScore ? intentFlow.flowScore.toFixed(1) : 0}/100, Avg confidence: ${intentFlow.averageConfidence ? (intentFlow.averageConfidence * 100).toFixed(1) : 0}%, Steps: ${intentFlow.completedSteps || 0}/${intentFlow.totalRequiredSteps || 20}`
            },
            // Enhanced breakdown with conversation quality
            conversationAnalysis: {
                totalTurns: intentFlow.intentMappings ? intentFlow.intentMappings.length : 0,
                averageConfidence: intentFlow.averageConfidence ? (intentFlow.averageConfidence * 100).toFixed(1) + '%' : '0%',
                completedSteps: intentFlow.completedSteps || 0,
                totalSteps: intentFlow.totalRequiredSteps || 20,
                missingCriticalSteps: intentFlow.missingCriticalSteps || [],
                conversationQuality: intentFlow.conversationQuality || { rating: 'Unknown', score: 0 }
            }
        };
        
        console.log(`✅ Overall score calculated: ${overallScore.toFixed(1)}/100`);
        return { overallScore, scoreBreakdown };
    }

    generateVisualizationData(audioData, silenceSegments) {
        console.log('📈 Generating visualization data...');
        
        const duration = audioData ? audioData.duration : 180; // Default 3 minutes
        
        // Generate mock waveform data for visualization
        const sampleRate = 100; // 100 samples per second for visualization
        const totalSamples = Math.floor(duration * sampleRate);
        const waveformData = [];
        
        for (let i = 0; i < totalSamples; i++) {
            const time = i / sampleRate;
            // Generate realistic waveform with some variation
            const amplitude = 0.5 * Math.sin(2 * Math.PI * 0.1 * time) * Math.exp(-time / 60) + 
                            0.3 * Math.random() - 0.15;
            waveformData.push({
                time,
                amplitude
            });
        }
        
        const visualizationData = {
            duration,
            waveformData,
            silenceMarkers: silenceSegments.map(segment => ({
                start: segment.startTime,
                end: segment.endTime,
                duration: segment.duration,
                speaker: segment.speaker
            })),
            sampleRate
        };
        
        console.log(`✅ Visualization data generated with ${silenceSegments.length} silence markers`);
        return visualizationData;
    }
}

module.exports = { CallQAAnalyzer };