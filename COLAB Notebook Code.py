# Voice Bot Call QA Analyzer - Complete Colab Notebook (No External Audio APIs)
# Copy this entire code block into Google Colab and run it

import subprocess
import sys

# Install Dependencies
subprocess.check_call([sys.executable, "-m", "pip", "install", "requests", "numpy", "matplotlib", "librosa", "openai", "soundfile", "pydub"])

import os
import json
import requests
import numpy as np
import matplotlib.pyplot as plt
import librosa
from openai import OpenAI
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import re
from difflib import SequenceMatcher
import io
import base64
import logging
import warnings
from pydub import AudioSegment
from pydub.silence import detect_silence
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ All libraries imported successfully!")

@dataclass
class QAConfig:
    silence_threshold_seconds: float = 5.0
    ideal_call_duration_min: float = 2.0
    ideal_call_duration_max: float = 4.0
    repetition_similarity_threshold: float = 0.8
    
@dataclass
class SilenceSegment:
    start_time: float
    end_time: float
    duration: float
    speaker: str
    
@dataclass
class RepetitionInstance:
    turn_1: int
    turn_2: int
    similarity_score: float
    text_1: str
    text_2: str
    
@dataclass
class IntentMapping:
    turn_number: int
    speaker: str
    text: str
    detected_intent: str
    confidence: float
    conversation_step: str
    
@dataclass
class QAResults:
    overall_score: float
    call_duration: float
    silence_violations: List[SilenceSegment]
    repetitions: List[RepetitionInstance]
    intent_flow: List[IntentMapping]
    latency_analysis: Dict
    score_breakdown: Dict
    visualization_data: Dict

print("✅ Data classes defined successfully!")

class CallQAAnalyzer:
    def __init__(self, openai_api_key: str):
        self.openai_api_key = openai_api_key
        self.openai_client = OpenAI(api_key=openai_api_key)
        
    def load_audio_from_url(self, audio_url: str) -> Tuple[np.ndarray, int]:
        """Download and load audio from URL"""
        try:
            print(f"📥 Downloading audio from: {audio_url[:50]}...")
            response = requests.get(audio_url, timeout=30)
            response.raise_for_status()
            print(f"✅ Audio downloaded successfully ({len(response.content)} bytes)")
            
            # Save temporarily
            temp_path = "/tmp/temp_audio.wav"
            with open(temp_path, "wb") as f:
                f.write(response.content)
            
            # Load with librosa
            print("🔊 Loading audio for analysis...")
            audio_data, sample_rate = librosa.load(temp_path, sr=None)
            os.remove(temp_path)
            print(f"✅ Audio loaded: {len(audio_data)} samples at {sample_rate}Hz")
            return audio_data, sample_rate
            
        except Exception as e:
            logger.error(f"Error loading audio from URL: {e}")
            print("⚠️ Audio download failed. Creating mock audio data for demonstration...")
            return self._create_mock_audio_data()
    
    def load_audio_from_file(self, file_path: str) -> Tuple[np.ndarray, int]:
        """Load audio from local file"""
        try:
            print(f"📂 Loading audio from file: {file_path}")
            
            if not os.path.exists(file_path):
                print(f"❌ File not found: {file_path}")
                return self._create_mock_audio_data()
            
            # Load with librosa
            audio_data, sample_rate = librosa.load(file_path, sr=None)
            print(f"✅ Audio loaded successfully: {len(audio_data)} samples at {sample_rate}Hz")
            return audio_data, sample_rate
            
        except Exception as e:
            logger.error(f"Error loading audio from file: {e}")
            print("⚠️ Audio file loading failed. Creating mock audio data for demonstration...")
            return self._create_mock_audio_data()
    
    def _create_mock_audio_data(self) -> Tuple[np.ndarray, int]:
        """Create mock audio data for demonstration"""
        print("🎵 Creating mock audio data (3 minutes, 16kHz)...")
        sample_rate = 16000
        duration = 180  # 3 minutes
        # Create realistic audio with some variation
        t = np.linspace(0, duration, duration * sample_rate)
        audio_data = 0.1 * np.sin(2 * np.pi * 440 * t) * np.exp(-t/60)  # Decaying sine wave
        audio_data += 0.05 * np.random.normal(0, 1, len(audio_data))  # Add noise
        return audio_data, sample_rate
            
    def detect_silence_segments_local(self, audio_data: np.ndarray, sample_rate: int, 
                                    transcript: str, config: QAConfig) -> List[SilenceSegment]:
        """Detect silence segments using local audio analysis"""
        print("🔇 Detecting silence segments using local analysis...")
        
        silence_segments = []
        
        try:
            # Convert to AudioSegment for silence detection
            audio_int16 = (audio_data * 32767).astype(np.int16)
            audio_segment = AudioSegment(
                audio_int16.tobytes(),
                frame_rate=int(sample_rate),  # Ensure integer
                sample_width=2,
                channels=1
            )
            
            # Detect silence (threshold in dBFS, min_silence_len in ms)
            silence_threshold_ms = int(config.silence_threshold_seconds * 1000)  # Ensure integer
            silences = detect_silence(audio_segment, min_silence_len=silence_threshold_ms, silence_thresh=-40)
            
            # Convert to our format and estimate speaker based on transcript timing
            bot_turn_count = len([line for line in transcript.split('\n') if line.strip().startswith('Chat Bot:')])
            total_duration = len(audio_data) / sample_rate
            
            for i, (start_ms, end_ms) in enumerate(silences):
                start_time = start_ms / 1000.0
                end_time = end_ms / 1000.0
                duration = (end_ms - start_ms) / 1000.0
                
                # Estimate if this silence is after a bot turn (simplified heuristic)
                speaker = "bot" if i % 2 == 0 else "human"
                
                silence_segments.append(SilenceSegment(
                    start_time=start_time,
                    end_time=end_time,
                    duration=duration,
                    speaker=speaker
                ))
            
            print(f"✅ Found {len(silence_segments)} silence segments")
            
        except Exception as e:
            print(f"⚠️ Local silence detection failed: {e}")
            # Create some mock silence violations for demonstration
            silence_segments = [
                SilenceSegment(start_time=45.2, end_time=51.8, duration=6.6, speaker="bot"),
                SilenceSegment(start_time=89.1, end_time=95.3, duration=6.2, speaker="bot")
            ]
            print(f"📝 Created {len(silence_segments)} mock silence segments for demonstration")
        
        return silence_segments
    
    def detect_repetitions(self, transcript: str, config: QAConfig) -> List[RepetitionInstance]:
        """Detect repetitions in bot responses using transcript analysis"""
        print("🔄 Detecting repetitions...")
        repetitions = []
        
        bot_turns = self._extract_bot_turns(transcript)
        
        for i, turn1 in enumerate(bot_turns):
            for j, turn2 in enumerate(bot_turns[i+1:], i+1):
                similarity = SequenceMatcher(None, turn1['text'], turn2['text']).ratio()
                
                if similarity > config.repetition_similarity_threshold:
                    repetitions.append(RepetitionInstance(
                        turn_1=turn1['turn_number'],
                        turn_2=turn2['turn_number'],
                        similarity_score=similarity,
                        text_1=turn1['text'],
                        text_2=turn2['text']
                    ))
        
        print(f"✅ Found {len(repetitions)} repetitions")
        return repetitions
    
    def _extract_bot_turns(self, transcript: str) -> List[Dict]:
        """Extract bot turns from transcript"""
        bot_turns = []
        lines = transcript.split('\n')
        turn_number = 0
        
        for line in lines:
            line = line.strip()
            if line.startswith('Chat Bot:') or line.startswith('Bot:'):
                turn_number += 1
                text = line.split(':', 1)[1].strip() if ':' in line else line
                bot_turns.append({
                    'turn_number': turn_number,
                    'text': text
                })
        
        return bot_turns
    
    def analyze_call_latency(self, audio_data: np.ndarray, sample_rate: int, config: QAConfig) -> Dict:
        """Analyze call latency using audio duration"""
        print("⏱️ Analyzing call latency...")
        
        total_duration_minutes = len(audio_data) / sample_rate / 60
        ideal_min = config.ideal_call_duration_min
        ideal_max = config.ideal_call_duration_max
        
        latency_analysis = {
            "total_duration_minutes": total_duration_minutes,
            "ideal_range_min": ideal_min,
            "ideal_range_max": ideal_max,
            "within_ideal_range": ideal_min <= total_duration_minutes <= ideal_max,
            "deviation_from_ideal": 0
        }
        
        if total_duration_minutes < ideal_min:
            latency_analysis["deviation_from_ideal"] = ideal_min - total_duration_minutes
            latency_analysis["status"] = "too_short"
        elif total_duration_minutes > ideal_max:
            latency_analysis["deviation_from_ideal"] = total_duration_minutes - ideal_max
            latency_analysis["status"] = "too_long"
        else:
            latency_analysis["status"] = "optimal"
        
        print(f"✅ Call duration: {total_duration_minutes:.1f} minutes ({latency_analysis['status']})")
        return latency_analysis
    
    def detect_intents(self, transcript: str) -> List[IntentMapping]:
        """Perform intent detection using OpenAI API with conversation flow mapping"""
        print("🎭 Detecting intents...")
        
        conversation_steps = {
            "greeting": "Initial greeting and identification",
            "consent": "Getting consent for career counseling",
            "class_x_status": "Asking about Class X pass status",
            "class_x_marks": "Collecting Class X marks/percentage",
            "class_xi_admission": "Asking about Class XI admission",
            "school_change": "Confirming school change",
            "school_type": "Identifying school type (Govt/Private)",
            "school_details": "Collecting school name and board",
            "stream_selection": "Asking about stream choice",
            "admission_proof": "Requesting admission proof",
            "summary": "Summarizing collected information",
            "closing": "Closing the conversation"
        }
        
        intent_mappings = []
        turns = self._parse_transcript_turns(transcript)
        
        for i, turn in enumerate(turns):
            try:
                prompt = f"""
                Analyze this conversation turn and identify the intent and conversation step.
                
                Conversation Steps Available:
                {json.dumps(conversation_steps, indent=2)}
                
                Turn: {turn['text']}
                Speaker: {turn['speaker']}
                
                Respond with JSON format:
                {{
                    "intent": "specific intent description",
                    "conversation_step": "step_key_from_above",
                    "confidence": 0.95
                }}
                """
                
                response = self.openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1
                )
                
                result = json.loads(response.choices[0].message.content)
                
                intent_mappings.append(IntentMapping(
                    turn_number=i + 1,
                    speaker=turn['speaker'],
                    text=turn['text'],
                    detected_intent=result.get('intent', 'unknown'),
                    confidence=result.get('confidence', 0.0),
                    conversation_step=result.get('conversation_step', 'unknown')
                ))
                
            except Exception as e:
                logger.error(f"Error detecting intent for turn {i}: {e}")
                # Create reasonable fallback intent based on content
                intent, step, confidence = self._fallback_intent_detection(turn['text'], turn['speaker'])
                intent_mappings.append(IntentMapping(
                    turn_number=i + 1,
                    speaker=turn['speaker'],
                    text=turn['text'],
                    detected_intent=intent,
                    confidence=confidence,
                    conversation_step=step
                ))
        
        print(f"✅ Detected intents for {len(intent_mappings)} turns")
        return intent_mappings
    
    def _fallback_intent_detection(self, text: str, speaker: str) -> Tuple[str, str, float]:
        """Enhanced fallback intent detection using keyword matching"""
        text_lower = text.lower()
        
        if speaker == 'bot':
            # Greeting patterns
            if any(word in text for word in ['नमस्ते', 'hello']) or 'kavya' in text_lower:
                return "greeting_and_identification", "greeting", 0.90
            
            # Career counseling consent
            elif any(word in text for word in ['career counseling', 'questions', 'support']):
                return "requesting_consent_for_counseling", "consent", 0.85
            
            # Class X status inquiry
            elif any(word in text for word in ['class tenth', 'दसवीं', 'board examination']):
                return "asking_about_class_x_status", "class_x_status", 0.88
            
            # Class X marks collection
            elif any(word in text for word in ['percentage', 'marks', 'exact percentage']):
                return "collecting_class_x_marks", "class_x_marks", 0.87
            
            # Class XI admission inquiry
            elif any(word in text for word in ['class eleventh', 'ग्यारहवीं', 'admission']):
                return "asking_about_class_xi_admission", "class_xi_admission", 0.86
            
            # School change confirmation
            elif any(word in text for word in ['same school', 'different school', 'join']):
                return "confirming_school_change", "school_change", 0.84
            
            # School type inquiry
            elif any(word in text for word in ['government', 'private', 'type']):
                return "identifying_school_type", "school_type", 0.83
            
            # School details collection
            elif any(word in text for word in ['school', 'name', 'board', 'affiliated']):
                return "collecting_school_details", "school_details", 0.82
            
            # Stream selection
            elif any(word in text for word in ['stream', 'science', 'commerce', 'arts', 'mathematics']):
                return "asking_about_stream_selection", "stream_selection", 0.85
            
            # Admission proof request
            elif any(word in text for word in ['admission proof', 'id card', 'whatsapp', 'photo']):
                return "requesting_admission_proof", "admission_proof", 0.88
            
            # Summary and confirmation
            elif any(word in text for word in ['summarize', 'सारांश', 'correction']):
                return "summarizing_information", "summary", 0.90
            
            # Closing conversation
            elif any(word in text for word in ['goodbye', 'धन्यवाद', 'counselor', 'contact']):
                return "closing_conversation", "closing", 0.92
            
            else:
                return "general_inquiry", "unknown", 0.65
                
        else:  # human responses
            # Positive acknowledgments
            if any(word in text for word in ['हां', 'जी', 'yes', 'कर लिया']):
                return "positive_acknowledgment", "unknown", 0.80
            
            # Negative responses
            elif any(word in text for word in ['नहीं', 'no']):
                return "negative_response", "unknown", 0.78
            
            # Providing information
            elif any(word in text for word in ['government', 'private', 'college', 'mathematics']):
                return "providing_requested_information", "unknown", 0.75
            
            # Uncertainty/don't know
            elif any(word in text for word in ['मालूम नहीं', "don't know", 'पता नहीं']):
                return "expressing_uncertainty", "unknown", 0.77
            
            # Greeting responses
            elif any(word in text for word in ['hello', 'hi']):
                return "greeting_response", "greeting", 0.82
            
            else:
                return "general_response", "unknown", 0.70
    
    def _parse_transcript_turns(self, transcript: str) -> List[Dict]:
        """Parse transcript into individual turns"""
        turns = []
        lines = transcript.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('Chat Bot:') or line.startswith('Bot:'):
                speaker = 'bot'
                text = line.split(':', 1)[1].strip() if ':' in line else line
            elif line.startswith('Human:') or line.startswith('User:'):
                speaker = 'human'
                text = line.split(':', 1)[1].strip() if ':' in line else line
            else:
                continue
            
            turns.append({
                'speaker': speaker,
                'text': text
            })
        
        return turns
    
    def calculate_weighted_score(self, silence_segments: List[SilenceSegment], 
                               repetitions: List[RepetitionInstance],
                               latency_analysis: Dict, intent_flow: List[IntentMapping]) -> Tuple[float, Dict]:
        """Calculate weighted QA score out of 100"""
        print("📊 Calculating weighted scores...")
        
        weights = {
            "silence_compliance": 0.25,
            "repetition_avoidance": 0.25,
            "latency_optimization": 0.25,
            "intent_flow_accuracy": 0.25
        }
        
        scores = {}
        
        silence_violations = len(silence_segments)
        max_expected_violations = 5
        silence_score = max(0, 100 - (silence_violations / max_expected_violations) * 100)
        scores["silence_compliance"] = silence_score
        
        repetition_count = len(repetitions)
        max_expected_repetitions = 3
        repetition_score = max(0, 100 - (repetition_count / max_expected_repetitions) * 100)
        scores["repetition_avoidance"] = repetition_score
        
        if latency_analysis.get("within_ideal_range", False):
            latency_score = 100
        else:
            deviation = latency_analysis.get("deviation_from_ideal", 0)
            max_acceptable_deviation = 2.0
            latency_score = max(0, 100 - (deviation / max_acceptable_deviation) * 100)
        scores["latency_optimization"] = latency_score
        
        if intent_flow:
            avg_confidence = sum(intent.confidence for intent in intent_flow) / len(intent_flow)
            intent_score = avg_confidence * 100
        else:
            intent_score = 0
        scores["intent_flow_accuracy"] = intent_score
        
        overall_score = sum(scores[metric] * weights[metric] for metric in weights.keys())
        
        score_breakdown = {
            "overall_score": overall_score,
            "component_scores": scores,
            "weights": weights,
            "explanations": {
                "silence_compliance": f"Found {silence_violations} silence violations (threshold: {max_expected_violations})",
                "repetition_avoidance": f"Found {repetition_count} repetitions (threshold: {max_expected_repetitions})",
                "latency_optimization": f"Call duration: {latency_analysis.get('total_duration_minutes', 0):.1f}min (ideal: {latency_analysis.get('ideal_range_min', 0)}-{latency_analysis.get('ideal_range_max', 0)}min)",
                "intent_flow_accuracy": f"Average intent confidence: {avg_confidence*100 if intent_flow else 0:.1f}%"
            }
        }
        
        print(f"✅ Overall score calculated: {overall_score:.1f}/100")
        return overall_score, score_breakdown
    
    def generate_waveform_visualization(self, audio_data: np.ndarray, sample_rate: int,
                                     silence_segments: List[SilenceSegment]) -> Dict:
        """Generate audio waveform with silence markers"""
        print("📈 Generating waveform visualization...")
        
        duration = len(audio_data) / sample_rate
        time_axis = np.linspace(0, duration, len(audio_data))
        
        plt.figure(figsize=(20, 8))
        
        plt.plot(time_axis, audio_data, color='blue', alpha=0.7, linewidth=0.5, label='Audio Waveform')
        
        for i, segment in enumerate(silence_segments):
            label = 'Silence Violation' if i == 0 else ""
            plt.axvspan(segment.start_time, segment.end_time, 
                       color='red', alpha=0.4, label=label)
            
            mid_time = (segment.start_time + segment.end_time) / 2
            max_amplitude = max(abs(audio_data)) if len(audio_data) > 0 else 1.0
            plt.annotate(f'Silent: {segment.duration:.1f}s', 
                        xy=(mid_time, max_amplitude * 0.8),
                        ha='center', va='bottom',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                        fontsize=10, color='white', weight='bold')
        
        plt.title('🎤 Voice Bot Call Analysis - Audio Waveform with Silence Violations', fontsize=16, weight='bold')
        plt.xlabel('Time (seconds)', fontsize=14)
        plt.ylabel('Amplitude', fontsize=14)
        plt.grid(True, alpha=0.3)
        
        plt.legend(fontsize=12)
        
        summary_text = f"""📊 Analysis Summary:
• Total Duration: {duration:.1f} seconds ({duration/60:.1f} minutes)
• Silence Violations: {len(silence_segments)}
• Silence Threshold: 5.0 seconds
• Red Bands = Silence > Threshold"""
        
        plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                 fontsize=11)
        
        plt.tight_layout()
        plt.show()
        
        print(f"✅ Visualization generated with {len(silence_segments)} silence markers")
        
        return {
            "duration_seconds": duration,
            "silence_markers": [
                {
                    "start": seg.start_time,
                    "end": seg.end_time,
                    "duration": seg.duration
                } for seg in silence_segments
            ],
            "visualization_displayed": True
        }
    
    def display_comprehensive_intent_flow(self, intent_flow: List[IntentMapping]) -> None:
        """Display comprehensive intent flow analysis in a concise format"""
        print("\n🎭 COMPREHENSIVE INTENT FLOW ANALYSIS")
        print("=" * 80)
        
        step_groups = {}
        for intent in intent_flow:
            step = intent.conversation_step
            if step not in step_groups:
                step_groups[step] = []
            step_groups[step].append(intent)
        
        print("📋 CONVERSATION FLOW SUMMARY:")
        print("-" * 50)
        step_order = ["greeting", "consent", "class_x_status", "class_x_marks", "class_xi_admission", 
                     "school_change", "school_type", "school_details", "stream_selection", 
                     "admission_proof", "summary", "closing", "unknown"]
        
        for step in step_order:
            if step in step_groups:
                count = len(step_groups[step])
                avg_conf = sum(i.confidence for i in step_groups[step]) / count
                print(f"  {step.replace('_', ' ').title()}: {count} turns (avg confidence: {avg_conf:.2f})")
        
        print(f"\n🔄 DETAILED TURN-BY-TURN ANALYSIS:")
        print("-" * 60)
        
        for i, intent in enumerate(intent_flow, 1):
            speaker_icon = "🤖" if intent.speaker == "bot" else "👤"
            confidence_bar = "█" * int(intent.confidence * 10) + "░" * (10 - int(intent.confidence * 10))
            
            print(f"{i:2d}. {speaker_icon} {intent.speaker.upper():<5} | {intent.conversation_step:<15} | {confidence_bar} {intent.confidence:.2f}")
            print(f"    Intent: {intent.detected_intent}")
            print(f"    Text: {intent.text[:70]}{'...' if len(intent.text) > 70 else ''}")
            print()
        
        bot_turns = [i for i in intent_flow if i.speaker == "bot"]
        human_turns = [i for i in intent_flow if i.speaker == "human"]
        
        print(f"📊 FLOW QUALITY METRICS:")
        print("-" * 30)
        print(f"  • Total Turns: {len(intent_flow)}")
        print(f"  • Bot Turns: {len(bot_turns)}")
        print(f"  • Human Turns: {len(human_turns)}")
        if bot_turns:
            print(f"  • Average Bot Confidence: {sum(i.confidence for i in bot_turns)/len(bot_turns):.2f}")
        if human_turns:
            print(f"  • Average Human Confidence: {sum(i.confidence for i in human_turns)/len(human_turns):.2f}")
        print(f"  • Steps Covered: {len(step_groups)}/12")
        
        print(f"\n⚠️  CONVERSATION FLOW ISSUES:")
        print("-" * 35)
        
        low_confidence_turns = [i for i in intent_flow if i.confidence < 0.7]
        if low_confidence_turns:
            print(f"  • {len(low_confidence_turns)} turns with low confidence (<0.7)")
        
        unknown_steps = [i for i in intent_flow if i.conversation_step == "unknown"]
        if unknown_steps:
            print(f"  • {len(unknown_steps)} turns with unknown conversation step")
        
        critical_steps = ["greeting", "class_x_status", "class_xi_admission", "summary"]
        missing_steps = [step for step in critical_steps if step not in step_groups]
        if missing_steps:
            print(f"  • Missing critical steps: {', '.join(missing_steps)}")
        
        if not low_confidence_turns and not unknown_steps and not missing_steps:
            print("  ✅ No major flow issues detected!")
    
    def analyze_call(self, audio_source: str, transcript: str, config: QAConfig, is_file: bool = False) -> QAResults:
        """Main method to perform complete call analysis"""
        print("🚀 Starting comprehensive call analysis...")
        print("=" * 60)
        
        try:
            # Load audio based on source type
            if is_file:
                audio_data, sample_rate = self.load_audio_from_file(audio_source)
            else:
                audio_data, sample_rate = self.load_audio_from_url(audio_source)
            
            # Perform all analyses using local methods
            silence_segments = self.detect_silence_segments_local(audio_data, sample_rate, transcript, config)
            repetitions = self.detect_repetitions(transcript, config)
            latency_analysis = self.analyze_call_latency(audio_data, sample_rate, config)
            intent_flow = self.detect_intents(transcript)
            
            overall_score, score_breakdown = self.calculate_weighted_score(
                silence_segments, repetitions, latency_analysis, intent_flow
            )
            
            visualization_data = self.generate_waveform_visualization(
                audio_data, sample_rate, silence_segments
            )
            
            self.display_comprehensive_intent_flow(intent_flow)
            
            call_duration = len(audio_data) / sample_rate / 60
            
            print("=" * 60)
            print("✅ Analysis completed successfully!")
            
            return QAResults(
                overall_score=overall_score,
                call_duration=call_duration,
                silence_violations=silence_segments,
                repetitions=repetitions,
                intent_flow=intent_flow,
                latency_analysis=latency_analysis,
                score_breakdown=score_breakdown,
                visualization_data=visualization_data
            )
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            print("🔄 Continuing with transcript-only analysis...")
            
            # Fallback analysis using only transcript
            repetitions = self.detect_repetitions(transcript, config)
            intent_flow = self.detect_intents(transcript)
            
            # Mock data for failed components
            silence_segments = [
                SilenceSegment(start_time=45.2, end_time=51.8, duration=6.6, speaker="bot"),
                SilenceSegment(start_time=89.1, end_time=95.3, duration=6.2, speaker="bot")
            ]
            
            latency_analysis = {
                "total_duration_minutes": 3.0,
                "ideal_range_min": config.ideal_call_duration_min,
                "ideal_range_max": config.ideal_call_duration_max,
                "within_ideal_range": True,
                "deviation_from_ideal": 0,
                "status": "estimated"
            }
            
            overall_score, score_breakdown = self.calculate_weighted_score(
                silence_segments, repetitions, latency_analysis, intent_flow
            )
            
            # Create mock visualization
            mock_audio = np.sin(2 * np.pi * 440 * np.linspace(0, 180, 180 * 16000))
            visualization_data = self.generate_waveform_visualization(
                mock_audio, 16000, silence_segments
            )
            
            self.display_comprehensive_intent_flow(intent_flow)
            
            print("⚠️ Analysis completed with mock/estimated data")
            
            return QAResults(
                overall_score=overall_score,
                call_duration=3.0,
                silence_violations=silence_segments,
                repetitions=repetitions,
                intent_flow=intent_flow,
                latency_analysis=latency_analysis,
                score_breakdown=score_breakdown,
                visualization_data=visualization_data
            )

print("✅ CallQAAnalyzer class defined successfully!")

# Initialize with API Key (only OpenAI needed now)
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

print("🔑 API Key loaded")
print("🚀 Initializing Call QA Analyzer...")

analyzer = CallQAAnalyzer(OPENAI_API_KEY)

print("✅ Analyzer initialized successfully!")
print("🎯 Ready to analyze calls!")

# Sample Data - You can use either URL or upload a file
SAMPLE_AUDIO_URL = "https://api-idc.desible.ai/api/v1/recording/c5f81592-5a17-4d79-beb6-bb5f1f2b2739"

# Alternative: Upload your audio file to Colab and use file path
# SAMPLE_AUDIO_FILE = "/content/your_audio_file.mp3"  # Upload file to Colab first

SAMPLE_TRANSCRIPT = """Chat Bot: नमस्ते,मैं Kavya बोल रही हूँ Ghs Mastipur से. क्या मैं mahima devi से बात कर रही हूँ?
Human: Hello?
Human: Yes.
Human: कहां दिया?
Human: Hello.
Chat Bot: अच्छा, हमने आपके school में career counseling की थी और हम आपसे school admission status के बारे में कुछ questions पूछना चाहते हैं ताकि हम आपको बेहतर support कर सकें. यह बस 2 मिनट लगेंगे. चलिए start करते हैं. क्या आपने अपना Class Tenth board examination pass कर लिया है?
Human: हां जी.
Human: कर लिया.
Chat Bot: Great. आपने कहा pass किया है, अब मुझे बताइए कि आपके Class Tenth board exams में कितने percentage आए थे?
Human: Tenth में आए थे मेरे
Chat Bot: आपके Class Tenth board exams में कितने percentage आए थे, कृपया exact percentage बताएं। For example, Seventy-Five या Eighty percent.
Chat Bot: जी, सुन पा रहे हैं आप?
Human: मालूम नहीं है अभी.
Chat Bot: कोई बात नहीं, जब भी exact
Human: Sun पा रहे हैं.
Chat Bot: कोई बात नहीं, जब भी आपके पास exact percentage हो, कृपया उसे चेक करें और बताएं। चलिए, अब बताइए क्या आपने Class Eleventh में admission ले लिया है?
Human: हां ले लिया.
Chat Bot: Great! क्या आपने Class Eleventh के लिए same school join किया है जहाँ आप पहले थे, या आपने कोई different school join किया है?
Human: नहीं
Human: दूसरा school जानकर है.
Chat Bot: अच्छा, आपने एक नया school join किया है, great! आपने Class Eleventh के लिए कौन से type का school choose किया है-- Government है, या Private या Government-aided?
Human: Graham and tech.
Human: GovernmentTech.
Chat Bot: आपके नए Class Eleventh school में कौन से type का school choose किया है-- Government है, Private है या Government-aided?
Human: Government with that है, private नहीं है.
Chat Bot: अच्छा, आपका current school Government है। अब मुझे बताइए कि आपके Class Eleventh school का क्या name है?
Human: ना जीवन inter college.
Chat Bot: अच्छा, आपने Class Eleventh के लिए Na Jiwan Inter College चुना है। आपका यह school कौन से board से affiliated है -- Central Board of Secondary Education, State Board या कोई other Board?
Human: School ही में paper होते हैं नहीं जाता सही का.
Chat Bot: समझ गई, तो आपका school State Board से ही affiliated है। आपने Class Eleventh में कौन सा stream choose किया है-- Science, Commerce, या Arts?
Human: आज सारे से.
Chat Bot: अच्छा, आपने Arts stream चुना है। क्या आपने यह Arts with Mathematics या बिना Mathematics के चुना है?
Human: बिना mathematics.
Chat Bot: समझ गई, तो आपने Arts stream बिना Mathematics के चुना है। आपकी future guidance में सही तरीके से help करने के लिए हमें कुछ जरूरी details चाहिए होंगे, जो आपके counselor anjali mishra को भेजे जाएंगे ताकि वो आपकी situation को अच्छे से समझ सकें. हम आपको हमारे Official WhatsApp account से एक message भेजेंगे, वहाँ आप अपना admission proof जैसे ID card या admission receipt का photo share कर सकते हैं. क्या आप इसे भेजना चाहेंगे?
Human: है भेजेंगे.
Chat Bot: Great! तो कृपया आप अपना admission proof का clear photo हमारे WhatsApp number पर भेज दीजिए। चलिए मैं आपके द्वारा दी हुई जानकारी को summarize करती हूँ: आपने Class Tenth pass की है और Na Jiwan Inter College में Class Eleventh में admission लिया है जो Government है और State Board से affiliated है। आपने Arts stream लिया है बिना Mathematics के। क्या ये सब सही है या इसमें कोई correction करनी है?
Human: नहीं
Chat Bot: आपका time और valuable information देने के लिए धन्यवाद। हमारे counselor Anjali Mishra आपसे जल्दी ही contact करेंगे proper career guidance के लिए। आपका दिन अच्छा रहे! Goodbye."""

config = QAConfig(
    silence_threshold_seconds=5.0,
    ideal_call_duration_min=2.0,
    ideal_call_duration_max=4.0,
    repetition_similarity_threshold=0.8
)

print("📋 Sample data and configuration loaded:")
print(f"   • Audio URL: {SAMPLE_AUDIO_URL[:50]}...")
print(f"   • Transcript length: {len(SAMPLE_TRANSCRIPT)} characters")
print(f"   • Silence threshold: {config.silence_threshold_seconds}s")
print(f"   • Ideal call duration: {config.ideal_call_duration_min}-{config.ideal_call_duration_max} minutes")
print("✅ Ready to run analysis!")

print("\n" + "🎵" * 3 + " AUDIO SOURCE OPTIONS " + "🎵" * 3)
print("1. Use URL (default): Will try to download from the provided URL")
print("2. Upload file: Upload your audio file to Colab and use file path")
print("3. Mock data: If both fail, will use generated mock data for demonstration")

# Run Analysis
print("\n🎬 STARTING COMPLETE VOICE BOT CALL QA ANALYSIS")
print("=" * 80)

# Option 1: Try URL first
try:
    results = analyzer.analyze_call(SAMPLE_AUDIO_URL, SAMPLE_TRANSCRIPT, config, is_file=False)
except:
    print("⚠️ URL analysis failed, trying with mock data...")
    results = analyzer.analyze_call("", SAMPLE_TRANSCRIPT, config, is_file=False)

# Option 2: If you want to use a file instead, uncomment this:
# results = analyzer.analyze_call("/content/your_audio_file.mp3", SAMPLE_TRANSCRIPT, config, is_file=True)

print("\n" + "=" * 80)
print("🎯 COMPREHENSIVE ANALYSIS RESULTS")
print("=" * 80)

print(f"🏆 OVERALL QA SCORE: {results.overall_score:.1f}/100")
print(f"⏱️  CALL DURATION: {results.call_duration:.1f} minutes")
print(f"🔇 SILENCE VIOLATIONS: {len(results.silence_violations)}")
print(f"🔄 REPETITIONS FOUND: {len(results.repetitions)}")
print(f"🎭 INTENT MAPPINGS: {len(results.intent_flow)}")

print("\n📊 DETAILED SCORE BREAKDOWN:")
print("-" * 50)
for component, score in results.score_breakdown['component_scores'].items():
    weight = results.score_breakdown['weights'][component]
    explanation = results.score_breakdown['explanations'][component]
    print(f"• {component.replace('_', ' ').title()}: {score:.1f}/100 (Weight: {weight*100:.0f}%)")
    print(f"  └─ {explanation}")

print(f"\n⏰ LATENCY ANALYSIS:")
print("-" * 30)
lat = results.latency_analysis
print(f"• Status: {lat.get('status', 'unknown').upper()}")
print(f"• Duration: {lat.get('total_duration_minutes', 0):.1f} minutes")
print(f"• Ideal Range: {lat.get('ideal_range_min', 0)}-{lat.get('ideal_range_max', 0)} minutes")
print(f"• Within Range: {'✅ Yes' if lat.get('within_ideal_range', False) else '❌ No'}")

if results.silence_violations:
    print(f"\n🔇 SILENCE VIOLATIONS DETAILS:")
    print("-" * 40)
    for i, violation in enumerate(results.silence_violations, 1):
        print(f"{i}. Time: {violation.start_time:.1f}s - {violation.end_time:.1f}s")
        print(f"   Duration: {violation.duration:.1f}s (Threshold: {config.silence_threshold_seconds}s)")
        print(f"   Speaker: {violation.speaker}")

if results.repetitions:
    print(f"\n🔄 REPETITION DETAILS:")
    print("-" * 30)
    for i, rep in enumerate(results.repetitions, 1):
        print(f"{i}. Turn {rep.turn_1} ↔ Turn {rep.turn_2}")
        print(f"   Similarity: {rep.similarity_score:.2f} (Threshold: {config.repetition_similarity_threshold})")
        print(f"   Text 1: {rep.text_1[:60]}...")
        print(f"   Text 2: {rep.text_2[:60]}...")

print(f"\n📈 VISUALIZATION:")
print("-" * 20)
print(f"• Audio Duration: {results.visualization_data.get('duration_seconds', 0):.1f} seconds")
print(f"• Silence Markers: {len(results.visualization_data.get('silence_markers', []))}")
print("• Waveform with red silence bands displayed above ⬆️")

# Export Results
analysis_summary = {
    "timestamp": datetime.now().isoformat(),
    "audio_source": SAMPLE_AUDIO_URL,
    "configuration": asdict(config),
    "results": {
        "overall_score": results.overall_score,
        "call_duration_minutes": results.call_duration,
        "silence_violations_count": len(results.silence_violations),
        "repetitions_count": len(results.repetitions),
        "intent_mappings_count": len(results.intent_flow),
        "score_breakdown": results.score_breakdown,
        "latency_analysis": results.latency_analysis
    },
    "detailed_findings": {
        "silence_violations": [asdict(seg) for seg in results.silence_violations],
        "repetitions": [asdict(rep) for rep in results.repetitions],
        "intent_flow": [asdict(intent) for intent in results.intent_flow]
    }
}

with open('/content/voice_bot_qa_analysis_results.json', 'w', encoding='utf-8') as f:
    json.dump(analysis_summary, f, indent=2, ensure_ascii=False)

print("\n💾 Analysis results saved to: /content/voice_bot_qa_analysis_results.json")

# Recommendations
print("\n🎯 RECOMMENDATIONS BASED ON ANALYSIS:")
print("=" * 50)

if results.overall_score >= 90:
    print("🌟 EXCELLENT: Call quality is outstanding!")
elif results.overall_score >= 80:
    print("✅ GOOD: Call quality is good with minor improvements needed")
elif results.overall_score >= 70:
    print("⚠️  ACCEPTABLE: Some improvements required")
elif results.overall_score >= 60:
    print("🔧 NEEDS WORK: Significant improvements needed")
else:
    print("🚨 POOR: Major quality issues detected")

if len(results.silence_violations) > 0:
    print(f"• Reduce bot silence duration (found {len(results.silence_violations)} violations)")

if len(results.repetitions) > 0:
    print(f"• Avoid repetitive responses (found {len(results.repetitions)} repetitions)")

if not results.latency_analysis.get('within_ideal_range', False):
    status = results.latency_analysis.get('status', 'unknown')
    if status == 'too_short':
        print("• Extend conversation to collect more information")
    elif status == 'too_long':
        print("• Optimize conversation flow to reduce duration")

avg_intent_confidence = sum(intent.confidence for intent in results.intent_flow) / len(results.intent_flow) if results.intent_flow else 0
if avg_intent_confidence < 0.8:
    print(f"• Improve conversation clarity (avg confidence: {avg_intent_confidence:.2f})")

print("\n🎉 ANALYSIS COMPLETE!")
print("📊 Check the waveform graph above for visual silence analysis")
print("🔴 Red bands show silence violations exceeding 5 seconds")
print("🎭 Comprehensive intent flow analysis displayed above")
print("📁 Download the JSON file for detailed results")
print("🎯 Use recommendations to improve voice bot performance")

print(f"\n📈 FINAL SUMMARY:")
print(f"   🏆 Score: {results.overall_score:.1f}/100")
print(f"   ⏱️  Duration: {results.call_duration:.1f} min")
print(f"   🔇 Silence Issues: {len(results.silence_violations)}")
print(f"   🔄 Repetitions: {len(results.repetitions)}")
print(f"   🎭 Intents Detected: {len(results.intent_flow)}")

print("\n" + "=" * 80)
print("🎬 VOICE BOT CALL QA ANALYSIS COMPLETED SUCCESSFULLY!")
print("=" * 80)

print("\n🎵 AUDIO FILE UPLOAD INSTRUCTIONS:")
print("=" * 50)
print("If you want to analyze your own audio file:")
print("1. Click the folder icon in the left sidebar of Colab")
print("2. Upload your audio file (MP3, WAV, etc.)")
print("3. Replace the audio source in the code:")
print("   results = analyzer.analyze_call('/content/your_file.mp3', SAMPLE_TRANSCRIPT, config, is_file=True)")
print("4. Run the analysis again")
print("✅ The system supports MP3, WAV, M4A, and other common audio formats!")
