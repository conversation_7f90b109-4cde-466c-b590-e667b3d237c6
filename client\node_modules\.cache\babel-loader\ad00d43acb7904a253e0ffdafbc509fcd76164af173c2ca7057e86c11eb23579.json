{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MVP\\\\client\\\\src\\\\components\\\\DetailedResults.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FileText, AlertTriangle, Repeat, Clock, MessageSquare } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DetailedResults = ({\n  results\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('summary');\n  const tabs = [{\n    id: 'summary',\n    label: 'Summary',\n    icon: FileText\n  }, {\n    id: 'silence',\n    label: 'Silence Analysis',\n    icon: AlertTriangle\n  }, {\n    id: 'repetitions',\n    label: 'Repetitions',\n    icon: Repeat\n  }, {\n    id: 'latency',\n    label: 'Latency Analysis',\n    icon: Clock\n  }, {\n    id: 'intentflow',\n    label: 'Intent Flow Detection',\n    icon: MessageSquare\n  }];\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const renderSummaryTab = () => {\n    var _results$silenceViola, _results$repetitions, _results$intentFlow, _results$intentFlow2, _results$silenceViola2, _results$repetitions2, _results$latencyAnaly, _results$intentFlow3, _results$intentFlow4, _results$intentFlow5, _results$intentFlow5$;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-blue-800 mb-3\",\n            children: \"Call Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Overall Score:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [Math.round(results.overallScore), \"/100\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Call Duration:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: formatTime(results.callDuration * 60)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Analysis Date:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: new Date(results.timestamp).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-green-800 mb-3\",\n            children: \"Quality Metrics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Silence Violations:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: ((_results$silenceViola = results.silenceViolations) === null || _results$silenceViola === void 0 ? void 0 : _results$silenceViola.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Repetitions Found:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: ((_results$repetitions = results.repetitions) === null || _results$repetitions === void 0 ? void 0 : _results$repetitions.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Intent Flow Score:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [Math.round(((_results$intentFlow = results.intentFlow) === null || _results$intentFlow === void 0 ? void 0 : _results$intentFlow.flowScore) || 0), \"/100\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Avg Intent Confidence:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [Math.round((((_results$intentFlow2 = results.intentFlow) === null || _results$intentFlow2 === void 0 ? void 0 : _results$intentFlow2.averageConfidence) || 0) * 100), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 p-4 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-gray-800 mb-3\",\n          children: \"Key Insights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-2 text-sm text-gray-700\",\n          children: [results.overallScore >= 85 && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 mt-1\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Excellent call quality with minimal issues detected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), ((_results$silenceViola2 = results.silenceViolations) === null || _results$silenceViola2 === void 0 ? void 0 : _results$silenceViola2.length) > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-orange-500 mt-1\",\n              children: \"\\u26A0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Found \", results.silenceViolations.length, \" silence violations that may impact user experience\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), ((_results$repetitions2 = results.repetitions) === null || _results$repetitions2 === void 0 ? void 0 : _results$repetitions2.length) > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500 mt-1\",\n              children: \"!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Detected \", results.repetitions.length, \" repetitive responses that should be addressed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), ((_results$latencyAnaly = results.latencyAnalysis) === null || _results$latencyAnaly === void 0 ? void 0 : _results$latencyAnaly.status) === 'optimal' && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 mt-1\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Call duration is within the ideal range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), (((_results$intentFlow3 = results.intentFlow) === null || _results$intentFlow3 === void 0 ? void 0 : _results$intentFlow3.flowScore) || 0) >= 80 && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 mt-1\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Excellent conversation flow with high intent detection confidence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), (((_results$intentFlow4 = results.intentFlow) === null || _results$intentFlow4 === void 0 ? void 0 : _results$intentFlow4.averageConfidence) || 0) < 0.5 && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-orange-500 mt-1\",\n              children: \"\\u26A0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Low average intent confidence may indicate unclear conversation patterns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), (((_results$intentFlow5 = results.intentFlow) === null || _results$intentFlow5 === void 0 ? void 0 : (_results$intentFlow5$ = _results$intentFlow5.missingCriticalSteps) === null || _results$intentFlow5$ === void 0 ? void 0 : _results$intentFlow5$.length) || 0) > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500 mt-1\",\n              children: \"!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Missing \", results.intentFlow.missingCriticalSteps.length, \" critical conversation steps\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 5\n    }, this);\n  };\n  const renderSilenceTab = () => {\n    var _results$silenceViola3;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: ((_results$silenceViola3 = results.silenceViolations) === null || _results$silenceViola3 === void 0 ? void 0 : _results$silenceViola3.length) > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-red-800 mb-2\",\n            children: [results.silenceViolations.length, \" Silence Violations Detected\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-700\",\n            children: \"Silence periods longer than 5.0 seconds can negatively impact user experience.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: results.silenceViolations.map((violation, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-red-200 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-red-800\",\n                children: [\"Violation #\", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 capitalize\",\n                children: [\"After \", violation.speaker, \" turn\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Start Time:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium\",\n                  children: formatTime(violation.startTime)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"End Time:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium\",\n                  children: formatTime(violation.endTime)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Duration:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-red-600\",\n                  children: [violation.duration.toFixed(1), \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-50 p-4 rounded-lg text-center\",\n        children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n          className: \"w-8 h-8 text-green-600 mx-auto mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-green-800\",\n          children: \"No Silence Violations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-green-700\",\n          children: \"All silence periods are within acceptable limits.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 5\n    }, this);\n  };\n  const renderRepetitionsTab = () => {\n    var _results$repetitions3;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: ((_results$repetitions3 = results.repetitions) === null || _results$repetitions3 === void 0 ? void 0 : _results$repetitions3.length) > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-orange-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-orange-800 mb-2\",\n            children: [results.repetitions.length, \" Repetitions Found\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-orange-700\",\n            children: \"Similar responses detected with 80%+ similarity that may indicate scripted or repetitive behavior.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: results.repetitions.map((repetition, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-orange-200 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-orange-800\",\n                children: [\"Repetition #\", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded\",\n                children: [Math.round(repetition.similarityScore * 100), \"% similar\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-3 rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: [\"Turn #\", repetition.turn1, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: repetition.text1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-3 rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-1\",\n                  children: [\"Turn #\", repetition.turn2, \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm\",\n                  children: repetition.text2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-50 p-4 rounded-lg text-center\",\n        children: [/*#__PURE__*/_jsxDEV(Repeat, {\n          className: \"w-8 h-8 text-green-600 mx-auto mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-green-800\",\n          children: \"No Repetitions Detected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-green-700\",\n          children: \"Bot responses show good variety and natural conversation flow.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 5\n    }, this);\n  };\n  const renderLatencyTab = () => {\n    var _results$latencyAnaly2, _results$latencyAnaly3, _results$latencyAnaly4, _results$latencyAnaly5, _results$latencyAnaly6, _results$latencyAnaly7, _results$latencyAnaly8, _results$latencyAnaly9, _results$latencyAnaly0, _results$latencyAnaly1, _results$latencyAnaly10, _results$latencyAnaly11, _results$latencyAnaly12, _results$latencyAnaly13, _results$latencyAnaly14, _results$latencyAnaly15, _results$latencyAnaly16, _results$latencyAnaly17, _results$latencyAnaly18, _results$latencyAnaly19;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 rounded-lg ${((_results$latencyAnaly2 = results.latencyAnalysis) === null || _results$latencyAnaly2 === void 0 ? void 0 : _results$latencyAnaly2.status) === 'optimal' ? 'bg-green-50' : ((_results$latencyAnaly3 = results.latencyAnalysis) === null || _results$latencyAnaly3 === void 0 ? void 0 : _results$latencyAnaly3.status) === 'too_short' ? 'bg-orange-50' : 'bg-red-50'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: `font-semibold mb-2 ${((_results$latencyAnaly4 = results.latencyAnalysis) === null || _results$latencyAnaly4 === void 0 ? void 0 : _results$latencyAnaly4.status) === 'optimal' ? 'text-green-800' : ((_results$latencyAnaly5 = results.latencyAnalysis) === null || _results$latencyAnaly5 === void 0 ? void 0 : _results$latencyAnaly5.status) === 'too_short' ? 'text-orange-800' : 'text-red-800'}`,\n          children: \"Call Duration Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm ${((_results$latencyAnaly6 = results.latencyAnalysis) === null || _results$latencyAnaly6 === void 0 ? void 0 : _results$latencyAnaly6.status) === 'optimal' ? 'text-green-700' : ((_results$latencyAnaly7 = results.latencyAnalysis) === null || _results$latencyAnaly7 === void 0 ? void 0 : _results$latencyAnaly7.status) === 'too_short' ? 'text-orange-700' : 'text-red-700'}`,\n          children: [((_results$latencyAnaly8 = results.latencyAnalysis) === null || _results$latencyAnaly8 === void 0 ? void 0 : _results$latencyAnaly8.status) === 'optimal' && 'Call duration is within the ideal range for effective communication.', ((_results$latencyAnaly9 = results.latencyAnalysis) === null || _results$latencyAnaly9 === void 0 ? void 0 : _results$latencyAnaly9.status) === 'too_short' && 'Call duration is shorter than ideal, which may indicate incomplete conversation.', ((_results$latencyAnaly0 = results.latencyAnalysis) === null || _results$latencyAnaly0 === void 0 ? void 0 : _results$latencyAnaly0.status) === 'too_long' && 'Call duration exceeds the ideal range, which may indicate inefficiency.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"font-semibold text-blue-800 mb-3\",\n            children: \"Current Metrics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Actual Duration:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [(_results$latencyAnaly1 = results.latencyAnalysis) === null || _results$latencyAnaly1 === void 0 ? void 0 : (_results$latencyAnaly10 = _results$latencyAnaly1.totalDurationMinutes) === null || _results$latencyAnaly10 === void 0 ? void 0 : _results$latencyAnaly10.toFixed(1), \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `font-medium capitalize ${((_results$latencyAnaly11 = results.latencyAnalysis) === null || _results$latencyAnaly11 === void 0 ? void 0 : _results$latencyAnaly11.status) === 'optimal' ? 'text-green-600' : ((_results$latencyAnaly12 = results.latencyAnalysis) === null || _results$latencyAnaly12 === void 0 ? void 0 : _results$latencyAnaly12.status) === 'too_short' ? 'text-orange-600' : 'text-red-600'}`,\n                children: (_results$latencyAnaly13 = results.latencyAnalysis) === null || _results$latencyAnaly13 === void 0 ? void 0 : (_results$latencyAnaly14 = _results$latencyAnaly13.status) === null || _results$latencyAnaly14 === void 0 ? void 0 : _results$latencyAnaly14.replace('_', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 13\n            }, this), ((_results$latencyAnaly15 = results.latencyAnalysis) === null || _results$latencyAnaly15 === void 0 ? void 0 : _results$latencyAnaly15.deviationFromIdeal) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Deviation:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-red-600\",\n                children: [results.latencyAnalysis.deviationFromIdeal.toFixed(1), \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"font-semibold text-gray-800 mb-3\",\n            children: \"Ideal Range\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Minimum:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [(_results$latencyAnaly16 = results.latencyAnalysis) === null || _results$latencyAnaly16 === void 0 ? void 0 : _results$latencyAnaly16.idealRangeMin, \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Maximum:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [(_results$latencyAnaly17 = results.latencyAnalysis) === null || _results$latencyAnaly17 === void 0 ? void 0 : _results$latencyAnaly17.idealRangeMax, \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Within Range:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `font-medium ${(_results$latencyAnaly18 = results.latencyAnalysis) !== null && _results$latencyAnaly18 !== void 0 && _results$latencyAnaly18.withinIdealRange ? 'text-green-600' : 'text-red-600'}`,\n                children: (_results$latencyAnaly19 = results.latencyAnalysis) !== null && _results$latencyAnaly19 !== void 0 && _results$latencyAnaly19.withinIdealRange ? 'Yes' : 'No'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 5\n    }, this);\n  };\n  const renderIntentFlowTab = () => {\n    const intentFlow = results.intentFlow;\n    const flowScore = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.flowScore) || 0;\n    const averageConfidence = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.averageConfidence) || 0;\n    const completedSteps = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.completedSteps) || 0;\n    const totalRequiredSteps = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.totalRequiredSteps) || 20;\n    const missingCriticalSteps = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.missingCriticalSteps) || [];\n    const conversationQuality = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.conversationQuality) || {\n      rating: 'Unknown',\n      score: 0\n    };\n    const intentMappings = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.intentMappings) || [];\n    const conversationContext = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.conversationContext) || {\n      name: 'Unknown Context'\n    };\n    const contextualAnalysis = (intentFlow === null || intentFlow === void 0 ? void 0 : intentFlow.contextualAnalysis) || {};\n    const getScoreColor = score => {\n      if (score >= 80) return 'text-green-600';\n      if (score >= 60) return 'text-yellow-600';\n      if (score >= 40) return 'text-orange-600';\n      return 'text-red-600';\n    };\n    const getScoreBackground = score => {\n      if (score >= 80) return 'bg-green-50 border-green-200';\n      if (score >= 60) return 'bg-yellow-50 border-yellow-200';\n      if (score >= 40) return 'bg-orange-50 border-orange-200';\n      return 'bg-red-50 border-red-200';\n    };\n    const criticalSteps = ['initial_greeting', 'identity_verification', 'class_x_status', 'marks_percentage', 'admission_status', 'summary_confirmation', 'closing_statement'];\n    const completedCriticalSteps = intentMappings.filter(mapping => criticalSteps.includes(mapping.conversationStep)).length;\n    const highConfidenceSteps = intentMappings.filter(mapping => mapping.confidence > 0.7).length;\n    const lowConfidenceSteps = intentMappings.filter(mapping => mapping.confidence < 0.5).length;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 p-4 bg-indigo-50 border border-indigo-200 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-indigo-800 mb-2\",\n          children: \"Detected Conversation Context\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-indigo-700 font-medium mb-2\",\n          children: conversationContext.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-indigo-600\",\n          children: \"The system identified this conversation type and adjusted scoring accordingly. Only relevant steps for this context are considered in the analysis.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 rounded-lg border ${getScoreBackground(flowScore)}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-gray-800\",\n            children: \"Contextual Intent Flow Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-2xl font-bold ${getScoreColor(flowScore)}`,\n            children: [Math.round(flowScore), \"/100\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-700 mb-3\",\n          children: [\"This score reflects how well the conversation followed the expected flow for the detected context (\", conversationContext.name.toLowerCase(), \") and the confidence in intent detection.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `text-lg font-bold ${getScoreColor(averageConfidence * 100)}`,\n              children: [Math.round(averageConfidence * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Avg Confidence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-blue-600\",\n              children: [completedSteps, \"/\", totalRequiredSteps]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Required Steps\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-purple-600\",\n              children: [completedCriticalSteps, \"/\", criticalSteps.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Critical Steps\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `text-lg font-bold ${conversationQuality.rating === 'Excellent' ? 'text-green-600' : conversationQuality.rating === 'Good' ? 'text-blue-600' : conversationQuality.rating === 'Fair' ? 'text-yellow-600' : 'text-red-600'}`,\n              children: conversationQuality.rating\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Quality Rating\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-blue-800 mb-3\",\n            children: \"Priority-Based Confidence Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: [\"High Confidence Steps (\", '>', \"70%)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-green-600\",\n                children: highConfidenceSteps\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Medium Confidence Steps (40-70%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-yellow-600\",\n                children: intentMappings.filter(m => m.confidence >= 0.4 && m.confidence <= 0.7).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: [\"Low Confidence Steps (\", '<', \"40%)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-red-600\",\n                children: lowConfidenceSteps\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Priority-Weighted Average\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `font-medium ${getScoreColor(averageConfidence * 100)}`,\n                children: [Math.round(averageConfidence * 100), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 p-2 bg-blue-100 rounded text-xs text-blue-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Smart Weighting:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), \" Critical steps and high-confidence interactions are prioritized. Vague responses receive reduced weightage for more accurate assessment.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-purple-50 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-purple-800 mb-3\",\n            children: \"Contextual Flow Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Context-Required Steps\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: [completedSteps, \"/\", totalRequiredSteps]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Required Steps Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-blue-600\",\n                children: [Math.round(contextualAnalysis.requiredStepsScore || 0), \"/40\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Conditional Steps Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-green-600\",\n                children: [Math.round(contextualAnalysis.conditionalScore || 0), \"/20\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Flow Coherence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-orange-600\",\n                children: [Math.round(contextualAnalysis.sequentialBonus || 0), \"/10\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), missingCriticalSteps.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 p-4 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-red-800 mb-3\",\n          children: \"\\u26A0\\uFE0F Missing Critical Steps\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-red-700 mb-3\",\n          children: \"These important conversation steps were not detected, which may impact the overall effectiveness:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2\",\n          children: missingCriticalSteps.map((step, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\",\n            children: step.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 p-4 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold text-gray-800 mb-3\",\n          children: \"Contextual Scoring Methodology\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3 p-3 bg-indigo-50 rounded text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Context-Aware Scoring:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), \" The system identifies the conversation type (\", conversationContext.name.toLowerCase(), \") and only evaluates steps relevant to that context, making the analysis more accurate and fair.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2 text-sm text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-500 mt-1\",\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Context Identification (20 points):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 21\n              }, this), \" Correctly identifying the conversation type\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 mt-1\",\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Required Steps (40 points):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 21\n              }, this), \" Completion of steps essential for this context\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-purple-500 mt-1\",\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conditional Steps (20 points):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 21\n              }, this), \" Context-specific steps (e.g., dropout investigation only if mentioned)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-orange-500 mt-1\",\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Priority-Based Confidence (10 points):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 21\n              }, this), \" Weighted average prioritizing critical steps and high-confidence interactions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500 mt-1\",\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Sequential Flow (10 points):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this), \" Logical progression through conversation steps\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 p-3 bg-yellow-50 rounded text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Smart Logic:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), \" Parent/relative steps only count if they answer the call. Dropout investigation only applies if student mentions dropping out. Wrong number and voicemail handling are separate contexts.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 p-3 bg-purple-50 rounded text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Priority-Based Confidence:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), \" Critical steps (4x weight), high-confidence steps (2.5x weight), clear messages (2x weight), while vague responses like \\\"\\u0939\\u093E\\u0901\\\", \\\"okay\\\", \\\"\\u0920\\u0940\\u0915 \\u0939\\u0948\\\" get reduced weightage (0.5x) for more meaningful confidence assessment.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-xl font-bold text-gray-900 mb-6\",\n      children: \"Detailed Analysis Results\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b border-gray-200 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex space-x-8\",\n        children: tabs.map(tab => {\n          const Icon = tab.icon;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), tab.label]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [activeTab === 'summary' && renderSummaryTab(), activeTab === 'silence' && renderSilenceTab(), activeTab === 'repetitions' && renderRepetitionsTab(), activeTab === 'latency' && renderLatencyTab(), activeTab === 'intentflow' && renderIntentFlowTab()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 507,\n    columnNumber: 5\n  }, this);\n};\n_s(DetailedResults, \"yf5WAnZcexj20AqU4wAdQYur0ak=\");\n_c = DetailedResults;\nexport default DetailedResults;\nvar _c;\n$RefreshReg$(_c, \"DetailedResults\");", "map": {"version": 3, "names": ["React", "useState", "FileText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Repeat", "Clock", "MessageSquare", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DetailedResults", "results", "_s", "activeTab", "setActiveTab", "tabs", "id", "label", "icon", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "renderSummaryTab", "_results$silenceViola", "_results$repetitions", "_results$intentFlow", "_results$intentFlow2", "_results$silenceViola2", "_results$repetitions2", "_results$latencyAnaly", "_results$intentFlow3", "_results$intentFlow4", "_results$intentFlow5", "_results$intentFlow5$", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "round", "overallScore", "callDuration", "Date", "timestamp", "toLocaleDateString", "silenceViolations", "length", "repetitions", "intentFlow", "flowScore", "averageConfidence", "latencyAnalysis", "status", "missingCriticalSteps", "renderSilenceTab", "_results$silenceViola3", "map", "violation", "index", "speaker", "startTime", "endTime", "duration", "toFixed", "renderRepetitionsTab", "_results$repetitions3", "repetition", "similarityScore", "turn1", "text1", "turn2", "text2", "renderLatencyTab", "_results$latencyAnaly2", "_results$latencyAnaly3", "_results$latencyAnaly4", "_results$latencyAnaly5", "_results$latencyAnaly6", "_results$latencyAnaly7", "_results$latencyAnaly8", "_results$latencyAnaly9", "_results$latencyAnaly0", "_results$latencyAnaly1", "_results$latencyAnaly10", "_results$latencyAnaly11", "_results$latencyAnaly12", "_results$latencyAnaly13", "_results$latencyAnaly14", "_results$latencyAnaly15", "_results$latencyAnaly16", "_results$latencyAnaly17", "_results$latencyAnaly18", "_results$latencyAnaly19", "totalDurationMinutes", "replace", "deviationFromIdeal", "idealRangeMin", "idealRangeMax", "withinIdealRange", "renderIntentFlowTab", "completedSteps", "totalRequiredSteps", "conversationQuality", "rating", "score", "intentMappings", "conversationContext", "name", "contextualAnalysis", "getScoreColor", "getScoreBackground", "criticalSteps", "completedCriticalSteps", "filter", "mapping", "includes", "conversationStep", "highConfidenceSteps", "confidence", "lowConfidenceSteps", "toLowerCase", "m", "requiredStepsScore", "conditionalScore", "sequentialBonus", "step", "l", "toUpperCase", "tab", "Icon", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/MVP/client/src/components/DetailedResults.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { FileText, AlertTriangle, Repeat, Clock, MessageSquare } from 'lucide-react';\r\n\r\nconst DetailedResults = ({ results }) => {\r\n  const [activeTab, setActiveTab] = useState('summary');\r\n\r\n  const tabs = [\r\n    { id: 'summary', label: 'Summary', icon: FileText },\r\n    { id: 'silence', label: 'Silence Analysis', icon: AlertTriangle },\r\n    { id: 'repetitions', label: 'Repetitions', icon: Repeat },\r\n    { id: 'latency', label: 'Latency Analysis', icon: Clock },\r\n    { id: 'intentflow', label: 'Intent Flow Detection', icon: MessageSquare }\r\n  ];\r\n\r\n  const formatTime = (seconds) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = Math.floor(seconds % 60);\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const renderSummaryTab = () => (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n        <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n          <h4 className=\"font-semibold text-blue-800 mb-3\">Call Overview</h4>\r\n          <div className=\"space-y-2 text-sm\">\r\n            <div className=\"flex justify-between\">\r\n              <span>Overall Score:</span>\r\n              <span className=\"font-medium\">{Math.round(results.overallScore)}/100</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Call Duration:</span>\r\n              <span className=\"font-medium\">{formatTime(results.callDuration * 60)}</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Analysis Date:</span>\r\n              <span className=\"font-medium\">{new Date(results.timestamp).toLocaleDateString()}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-green-50 p-4 rounded-lg\">\r\n          <h4 className=\"font-semibold text-green-800 mb-3\">Quality Metrics</h4>\r\n          <div className=\"space-y-2 text-sm\">\r\n            <div className=\"flex justify-between\">\r\n              <span>Silence Violations:</span>\r\n              <span className=\"font-medium\">{results.silenceViolations?.length || 0}</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Repetitions Found:</span>\r\n              <span className=\"font-medium\">{results.repetitions?.length || 0}</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Intent Flow Score:</span>\r\n              <span className=\"font-medium\">{Math.round(results.intentFlow?.flowScore || 0)}/100</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Avg Intent Confidence:</span>\r\n              <span className=\"font-medium\">{Math.round((results.intentFlow?.averageConfidence || 0) * 100)}%</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n        <h4 className=\"font-semibold text-gray-800 mb-3\">Key Insights</h4>\r\n        <ul className=\"space-y-2 text-sm text-gray-700\">\r\n          {results.overallScore >= 85 && (\r\n            <li className=\"flex items-start gap-2\">\r\n              <span className=\"text-green-500 mt-1\">✓</span>\r\n              <span>Excellent call quality with minimal issues detected</span>\r\n            </li>\r\n          )}\r\n          {results.silenceViolations?.length > 0 && (\r\n            <li className=\"flex items-start gap-2\">\r\n              <span className=\"text-orange-500 mt-1\">⚠</span>\r\n              <span>Found {results.silenceViolations.length} silence violations that may impact user experience</span>\r\n            </li>\r\n          )}\r\n          {results.repetitions?.length > 0 && (\r\n            <li className=\"flex items-start gap-2\">\r\n              <span className=\"text-red-500 mt-1\">!</span>\r\n              <span>Detected {results.repetitions.length} repetitive responses that should be addressed</span>\r\n            </li>\r\n          )}\r\n          {results.latencyAnalysis?.status === 'optimal' && (\r\n            <li className=\"flex items-start gap-2\">\r\n              <span className=\"text-green-500 mt-1\">✓</span>\r\n              <span>Call duration is within the ideal range</span>\r\n            </li>\r\n          )}\r\n          {(results.intentFlow?.flowScore || 0) >= 80 && (\r\n            <li className=\"flex items-start gap-2\">\r\n              <span className=\"text-green-500 mt-1\">✓</span>\r\n              <span>Excellent conversation flow with high intent detection confidence</span>\r\n            </li>\r\n          )}\r\n          {(results.intentFlow?.averageConfidence || 0) < 0.5 && (\r\n            <li className=\"flex items-start gap-2\">\r\n              <span className=\"text-orange-500 mt-1\">⚠</span>\r\n              <span>Low average intent confidence may indicate unclear conversation patterns</span>\r\n            </li>\r\n          )}\r\n          {(results.intentFlow?.missingCriticalSteps?.length || 0) > 0 && (\r\n            <li className=\"flex items-start gap-2\">\r\n              <span className=\"text-red-500 mt-1\">!</span>\r\n              <span>Missing {results.intentFlow.missingCriticalSteps.length} critical conversation steps</span>\r\n            </li>\r\n          )}\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderSilenceTab = () => (\r\n    <div className=\"space-y-4\">\r\n      {results.silenceViolations?.length > 0 ? (\r\n        <>\r\n          <div className=\"bg-red-50 p-4 rounded-lg\">\r\n            <h4 className=\"font-semibold text-red-800 mb-2\">\r\n              {results.silenceViolations.length} Silence Violations Detected\r\n            </h4>\r\n            <p className=\"text-sm text-red-700\">\r\n              Silence periods longer than 5.0 seconds can negatively impact user experience.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"space-y-3\">\r\n            {results.silenceViolations.map((violation, index) => (\r\n              <div key={index} className=\"border border-red-200 rounded-lg p-4\">\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"font-medium text-red-800\">\r\n                    Violation #{index + 1}\r\n                  </span>\r\n                  <span className=\"text-sm text-gray-600 capitalize\">\r\n                    After {violation.speaker} turn\r\n                  </span>\r\n                </div>\r\n                <div className=\"grid grid-cols-3 gap-4 text-sm\">\r\n                  <div>\r\n                    <span className=\"text-gray-600\">Start Time:</span>\r\n                    <div className=\"font-medium\">{formatTime(violation.startTime)}</div>\r\n                  </div>\r\n                  <div>\r\n                    <span className=\"text-gray-600\">End Time:</span>\r\n                    <div className=\"font-medium\">{formatTime(violation.endTime)}</div>\r\n                  </div>\r\n                  <div>\r\n                    <span className=\"text-gray-600\">Duration:</span>\r\n                    <div className=\"font-medium text-red-600\">{violation.duration.toFixed(1)}s</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <div className=\"bg-green-50 p-4 rounded-lg text-center\">\r\n          <AlertTriangle className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\r\n          <h4 className=\"font-semibold text-green-800\">No Silence Violations</h4>\r\n          <p className=\"text-sm text-green-700\">All silence periods are within acceptable limits.</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  const renderRepetitionsTab = () => (\r\n    <div className=\"space-y-4\">\r\n      {results.repetitions?.length > 0 ? (\r\n        <>\r\n          <div className=\"bg-orange-50 p-4 rounded-lg\">\r\n            <h4 className=\"font-semibold text-orange-800 mb-2\">\r\n              {results.repetitions.length} Repetitions Found\r\n            </h4>\r\n            <p className=\"text-sm text-orange-700\">\r\n              Similar responses detected with 80%+ similarity that may indicate scripted or repetitive behavior.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"space-y-3\">\r\n            {results.repetitions.map((repetition, index) => (\r\n              <div key={index} className=\"border border-orange-200 rounded-lg p-4\">\r\n                <div className=\"flex items-center justify-between mb-3\">\r\n                  <span className=\"font-medium text-orange-800\">\r\n                    Repetition #{index + 1}\r\n                  </span>\r\n                  <span className=\"text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded\">\r\n                    {Math.round(repetition.similarityScore * 100)}% similar\r\n                  </span>\r\n                </div>\r\n                \r\n                <div className=\"space-y-3\">\r\n                  <div className=\"bg-gray-50 p-3 rounded\">\r\n                    <div className=\"text-sm text-gray-600 mb-1\">Turn #{repetition.turn1}:</div>\r\n                    <div className=\"text-sm\">{repetition.text1}</div>\r\n                  </div>\r\n                  <div className=\"bg-gray-50 p-3 rounded\">\r\n                    <div className=\"text-sm text-gray-600 mb-1\">Turn #{repetition.turn2}:</div>\r\n                    <div className=\"text-sm\">{repetition.text2}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <div className=\"bg-green-50 p-4 rounded-lg text-center\">\r\n          <Repeat className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\r\n          <h4 className=\"font-semibold text-green-800\">No Repetitions Detected</h4>\r\n          <p className=\"text-sm text-green-700\">Bot responses show good variety and natural conversation flow.</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  const renderLatencyTab = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className={`p-4 rounded-lg ${\r\n        results.latencyAnalysis?.status === 'optimal' ? 'bg-green-50' : \r\n        results.latencyAnalysis?.status === 'too_short' ? 'bg-orange-50' : 'bg-red-50'\r\n      }`}>\r\n        <h4 className={`font-semibold mb-2 ${\r\n          results.latencyAnalysis?.status === 'optimal' ? 'text-green-800' : \r\n          results.latencyAnalysis?.status === 'too_short' ? 'text-orange-800' : 'text-red-800'\r\n        }`}>\r\n          Call Duration Analysis\r\n        </h4>\r\n        <p className={`text-sm ${\r\n          results.latencyAnalysis?.status === 'optimal' ? 'text-green-700' : \r\n          results.latencyAnalysis?.status === 'too_short' ? 'text-orange-700' : 'text-red-700'\r\n        }`}>\r\n          {results.latencyAnalysis?.status === 'optimal' && 'Call duration is within the ideal range for effective communication.'}\r\n          {results.latencyAnalysis?.status === 'too_short' && 'Call duration is shorter than ideal, which may indicate incomplete conversation.'}\r\n          {results.latencyAnalysis?.status === 'too_long' && 'Call duration exceeds the ideal range, which may indicate inefficiency.'}\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n          <h5 className=\"font-semibold text-blue-800 mb-3\">Current Metrics</h5>\r\n          <div className=\"space-y-2 text-sm\">\r\n            <div className=\"flex justify-between\">\r\n              <span>Actual Duration:</span>\r\n              <span className=\"font-medium\">\r\n                {results.latencyAnalysis?.totalDurationMinutes?.toFixed(1)} minutes\r\n              </span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Status:</span>\r\n              <span className={`font-medium capitalize ${\r\n                results.latencyAnalysis?.status === 'optimal' ? 'text-green-600' : \r\n                results.latencyAnalysis?.status === 'too_short' ? 'text-orange-600' : 'text-red-600'\r\n              }`}>\r\n                {results.latencyAnalysis?.status?.replace('_', ' ')}\r\n              </span>\r\n            </div>\r\n            {results.latencyAnalysis?.deviationFromIdeal > 0 && (\r\n              <div className=\"flex justify-between\">\r\n                <span>Deviation:</span>\r\n                <span className=\"font-medium text-red-600\">\r\n                  {results.latencyAnalysis.deviationFromIdeal.toFixed(1)} minutes\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n          <h5 className=\"font-semibold text-gray-800 mb-3\">Ideal Range</h5>\r\n          <div className=\"space-y-2 text-sm\">\r\n            <div className=\"flex justify-between\">\r\n              <span>Minimum:</span>\r\n              <span className=\"font-medium\">\r\n                {results.latencyAnalysis?.idealRangeMin} minutes\r\n              </span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Maximum:</span>\r\n              <span className=\"font-medium\">\r\n                {results.latencyAnalysis?.idealRangeMax} minutes\r\n              </span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Within Range:</span>\r\n              <span className={`font-medium ${\r\n                results.latencyAnalysis?.withinIdealRange ? 'text-green-600' : 'text-red-600'\r\n              }`}>\r\n                {results.latencyAnalysis?.withinIdealRange ? 'Yes' : 'No'}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderIntentFlowTab = () => {\r\n    const intentFlow = results.intentFlow;\r\n    const flowScore = intentFlow?.flowScore || 0;\r\n    const averageConfidence = intentFlow?.averageConfidence || 0;\r\n    const completedSteps = intentFlow?.completedSteps || 0;\r\n    const totalRequiredSteps = intentFlow?.totalRequiredSteps || 20;\r\n    const missingCriticalSteps = intentFlow?.missingCriticalSteps || [];\r\n    const conversationQuality = intentFlow?.conversationQuality || { rating: 'Unknown', score: 0 };\r\n    const intentMappings = intentFlow?.intentMappings || [];\r\n    const conversationContext = intentFlow?.conversationContext || { name: 'Unknown Context' };\r\n    const contextualAnalysis = intentFlow?.contextualAnalysis || {};\r\n\r\n    const getScoreColor = (score) => {\r\n      if (score >= 80) return 'text-green-600';\r\n      if (score >= 60) return 'text-yellow-600';\r\n      if (score >= 40) return 'text-orange-600';\r\n      return 'text-red-600';\r\n    };\r\n\r\n    const getScoreBackground = (score) => {\r\n      if (score >= 80) return 'bg-green-50 border-green-200';\r\n      if (score >= 60) return 'bg-yellow-50 border-yellow-200';\r\n      if (score >= 40) return 'bg-orange-50 border-orange-200';\r\n      return 'bg-red-50 border-red-200';\r\n    };\r\n\r\n    const criticalSteps = [\r\n      'initial_greeting', 'identity_verification', 'class_x_status',\r\n      'marks_percentage', 'admission_status', 'summary_confirmation', 'closing_statement'\r\n    ];\r\n\r\n    const completedCriticalSteps = intentMappings.filter(mapping =>\r\n      criticalSteps.includes(mapping.conversationStep)\r\n    ).length;\r\n\r\n    const highConfidenceSteps = intentMappings.filter(mapping => mapping.confidence > 0.7).length;\r\n    const lowConfidenceSteps = intentMappings.filter(mapping => mapping.confidence < 0.5).length;\r\n\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        {/* Conversation Context */}\r\n        <div className=\"mb-6 p-4 bg-indigo-50 border border-indigo-200 rounded-lg\">\r\n          <h4 className=\"font-semibold text-indigo-800 mb-2\">Detected Conversation Context</h4>\r\n          <p className=\"text-indigo-700 font-medium mb-2\">{conversationContext.name}</p>\r\n          <p className=\"text-sm text-indigo-600\">\r\n            The system identified this conversation type and adjusted scoring accordingly.\r\n            Only relevant steps for this context are considered in the analysis.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Overall Flow Score */}\r\n        <div className={`p-4 rounded-lg border ${getScoreBackground(flowScore)}`}>\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <h4 className=\"font-semibold text-gray-800\">Contextual Intent Flow Score</h4>\r\n            <span className={`text-2xl font-bold ${getScoreColor(flowScore)}`}>\r\n              {Math.round(flowScore)}/100\r\n            </span>\r\n          </div>\r\n          <p className=\"text-sm text-gray-700 mb-3\">\r\n            This score reflects how well the conversation followed the expected flow for the detected context\r\n            ({conversationContext.name.toLowerCase()}) and the confidence in intent detection.\r\n          </p>\r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\r\n            <div className=\"text-center\">\r\n              <div className={`text-lg font-bold ${getScoreColor(averageConfidence * 100)}`}>\r\n                {Math.round(averageConfidence * 100)}%\r\n              </div>\r\n              <div className=\"text-gray-600\">Avg Confidence</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg font-bold text-blue-600\">\r\n                {completedSteps}/{totalRequiredSteps}\r\n              </div>\r\n              <div className=\"text-gray-600\">Required Steps</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg font-bold text-purple-600\">\r\n                {completedCriticalSteps}/{criticalSteps.length}\r\n              </div>\r\n              <div className=\"text-gray-600\">Critical Steps</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className={`text-lg font-bold ${\r\n                conversationQuality.rating === 'Excellent' ? 'text-green-600' :\r\n                conversationQuality.rating === 'Good' ? 'text-blue-600' :\r\n                conversationQuality.rating === 'Fair' ? 'text-yellow-600' : 'text-red-600'\r\n              }`}>\r\n                {conversationQuality.rating}\r\n              </div>\r\n              <div className=\"text-gray-600\">Quality Rating</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Score Breakdown */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n            <h4 className=\"font-semibold text-blue-800 mb-3\">Priority-Based Confidence Analysis</h4>\r\n            <div className=\"space-y-3\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">High Confidence Steps ({'>'}70%)</span>\r\n                <span className=\"font-medium text-green-600\">{highConfidenceSteps}</span>\r\n              </div>\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">Medium Confidence Steps (40-70%)</span>\r\n                <span className=\"font-medium text-yellow-600\">{intentMappings.filter(m => m.confidence >= 0.4 && m.confidence <= 0.7).length}</span>\r\n              </div>\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">Low Confidence Steps ({'<'}40%)</span>\r\n                <span className=\"font-medium text-red-600\">{lowConfidenceSteps}</span>\r\n              </div>\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">Priority-Weighted Average</span>\r\n                <span className={`font-medium ${getScoreColor(averageConfidence * 100)}`}>\r\n                  {Math.round(averageConfidence * 100)}%\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"mt-3 p-2 bg-blue-100 rounded text-xs text-blue-700\">\r\n              <strong>Smart Weighting:</strong> Critical steps and high-confidence interactions are prioritized.\r\n              Vague responses receive reduced weightage for more accurate assessment.\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-purple-50 p-4 rounded-lg\">\r\n            <h4 className=\"font-semibold text-purple-800 mb-3\">Contextual Flow Analysis</h4>\r\n            <div className=\"space-y-3\">\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">Context-Required Steps</span>\r\n                <span className=\"font-medium\">{completedSteps}/{totalRequiredSteps}</span>\r\n              </div>\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">Required Steps Score</span>\r\n                <span className=\"font-medium text-blue-600\">{Math.round(contextualAnalysis.requiredStepsScore || 0)}/40</span>\r\n              </div>\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">Conditional Steps Score</span>\r\n                <span className=\"font-medium text-green-600\">{Math.round(contextualAnalysis.conditionalScore || 0)}/20</span>\r\n              </div>\r\n              <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm\">Flow Coherence</span>\r\n                <span className=\"font-medium text-orange-600\">{Math.round(contextualAnalysis.sequentialBonus || 0)}/10</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Missing Critical Steps */}\r\n        {missingCriticalSteps.length > 0 && (\r\n          <div className=\"bg-red-50 border border-red-200 p-4 rounded-lg\">\r\n            <h4 className=\"font-semibold text-red-800 mb-3\">⚠️ Missing Critical Steps</h4>\r\n            <p className=\"text-sm text-red-700 mb-3\">\r\n              These important conversation steps were not detected, which may impact the overall effectiveness:\r\n            </p>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {missingCriticalSteps.map((step, index) => (\r\n                <span key={index} className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">\r\n                  {step.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\r\n                </span>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Enhanced Score Explanation */}\r\n        <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n          <h4 className=\"font-semibold text-gray-800 mb-3\">Contextual Scoring Methodology</h4>\r\n          <div className=\"mb-3 p-3 bg-indigo-50 rounded text-sm\">\r\n            <strong>Context-Aware Scoring:</strong> The system identifies the conversation type\r\n            ({conversationContext.name.toLowerCase()}) and only evaluates steps relevant to that context,\r\n            making the analysis more accurate and fair.\r\n          </div>\r\n          <div className=\"space-y-2 text-sm text-gray-700\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <span className=\"text-blue-500 mt-1\">•</span>\r\n              <span><strong>Context Identification (20 points):</strong> Correctly identifying the conversation type</span>\r\n            </div>\r\n            <div className=\"flex items-start gap-2\">\r\n              <span className=\"text-green-500 mt-1\">•</span>\r\n              <span><strong>Required Steps (40 points):</strong> Completion of steps essential for this context</span>\r\n            </div>\r\n            <div className=\"flex items-start gap-2\">\r\n              <span className=\"text-purple-500 mt-1\">•</span>\r\n              <span><strong>Conditional Steps (20 points):</strong> Context-specific steps (e.g., dropout investigation only if mentioned)</span>\r\n            </div>\r\n            <div className=\"flex items-start gap-2\">\r\n              <span className=\"text-orange-500 mt-1\">•</span>\r\n              <span><strong>Priority-Based Confidence (10 points):</strong> Weighted average prioritizing critical steps and high-confidence interactions</span>\r\n            </div>\r\n            <div className=\"flex items-start gap-2\">\r\n              <span className=\"text-red-500 mt-1\">•</span>\r\n              <span><strong>Sequential Flow (10 points):</strong> Logical progression through conversation steps</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-3 p-3 bg-yellow-50 rounded text-sm\">\r\n            <strong>Smart Logic:</strong> Parent/relative steps only count if they answer the call.\r\n            Dropout investigation only applies if student mentions dropping out.\r\n            Wrong number and voicemail handling are separate contexts.\r\n          </div>\r\n          <div className=\"mt-2 p-3 bg-purple-50 rounded text-sm\">\r\n            <strong>Priority-Based Confidence:</strong> Critical steps (4x weight), high-confidence steps (2.5x weight),\r\n            clear messages (2x weight), while vague responses like \"हाँ\", \"okay\", \"ठीक है\" get reduced weightage (0.5x)\r\n            for more meaningful confidence assessment.\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"card\">\r\n      <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Detailed Analysis Results</h3>\r\n      \r\n      {/* Tab Navigation */}\r\n      <div className=\"border-b border-gray-200 mb-6\">\r\n        <nav className=\"flex space-x-8\">\r\n          {tabs.map((tab) => {\r\n            const Icon = tab.icon;\r\n            return (\r\n              <button\r\n                key={tab.id}\r\n                onClick={() => setActiveTab(tab.id)}\r\n                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${\r\n                  activeTab === tab.id\r\n                    ? 'border-blue-500 text-blue-600'\r\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                }`}\r\n              >\r\n                <Icon className=\"w-4 h-4\" />\r\n                {tab.label}\r\n              </button>\r\n            );\r\n          })}\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div>\r\n        {activeTab === 'summary' && renderSummaryTab()}\r\n        {activeTab === 'silence' && renderSilenceTab()}\r\n        {activeTab === 'repetitions' && renderRepetitionsTab()}\r\n        {activeTab === 'latency' && renderLatencyTab()}\r\n        {activeTab === 'intentflow' && renderIntentFlowTab()}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailedResults;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErF,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAMe,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEjB;EAAS,CAAC,EACnD;IAAEe,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEhB;EAAc,CAAC,EACjE;IAAEc,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAEf;EAAO,CAAC,EACzD;IAAEa,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEd;EAAM,CAAC,EACzD;IAAEY,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,IAAI,EAAEb;EAAc,CAAC,CAC1E;EAED,MAAMc,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGF,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,OAAO,IAAIG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA;IAAA,oBACvB/B,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjC,OAAA;QAAKgC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDjC,OAAA;UAAKgC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCjC,OAAA;YAAIgC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnErC,OAAA;YAAKgC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAElB,IAAI,CAACuB,KAAK,CAAClC,OAAO,CAACmC,YAAY,CAAC,EAAC,MAAI;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAErB,UAAU,CAACR,OAAO,CAACoC,YAAY,GAAG,EAAE;cAAC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE,IAAIQ,IAAI,CAACrC,OAAO,CAACsC,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCjC,OAAA;YAAIgC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtErC,OAAA;YAAKgC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChCrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE,EAAAZ,qBAAA,GAAAjB,OAAO,CAACwC,iBAAiB,cAAAvB,qBAAA,uBAAzBA,qBAAA,CAA2BwB,MAAM,KAAI;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE,EAAAX,oBAAA,GAAAlB,OAAO,CAAC0C,WAAW,cAAAxB,oBAAA,uBAAnBA,oBAAA,CAAqBuB,MAAM,KAAI;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAElB,IAAI,CAACuB,KAAK,CAAC,EAAAf,mBAAA,GAAAnB,OAAO,CAAC2C,UAAU,cAAAxB,mBAAA,uBAAlBA,mBAAA,CAAoByB,SAAS,KAAI,CAAC,CAAC,EAAC,MAAI;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnCrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAElB,IAAI,CAACuB,KAAK,CAAC,CAAC,EAAAd,oBAAA,GAAApB,OAAO,CAAC2C,UAAU,cAAAvB,oBAAA,uBAAlBA,oBAAA,CAAoByB,iBAAiB,KAAI,CAAC,IAAI,GAAG,CAAC,EAAC,GAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCjC,OAAA;UAAIgC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClErC,OAAA;UAAIgC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,GAC5C7B,OAAO,CAACmC,YAAY,IAAI,EAAE,iBACzBvC,OAAA;YAAIgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACpCjC,OAAA;cAAMgC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CrC,OAAA;cAAAiC,QAAA,EAAM;YAAmD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACL,EACA,EAAAZ,sBAAA,GAAArB,OAAO,CAACwC,iBAAiB,cAAAnB,sBAAA,uBAAzBA,sBAAA,CAA2BoB,MAAM,IAAG,CAAC,iBACpC7C,OAAA;YAAIgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACpCjC,OAAA;cAAMgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CrC,OAAA;cAAAiC,QAAA,GAAM,QAAM,EAAC7B,OAAO,CAACwC,iBAAiB,CAACC,MAAM,EAAC,qDAAmD;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CACL,EACA,EAAAX,qBAAA,GAAAtB,OAAO,CAAC0C,WAAW,cAAApB,qBAAA,uBAAnBA,qBAAA,CAAqBmB,MAAM,IAAG,CAAC,iBAC9B7C,OAAA;YAAIgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACpCjC,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CrC,OAAA;cAAAiC,QAAA,GAAM,WAAS,EAAC7B,OAAO,CAAC0C,WAAW,CAACD,MAAM,EAAC,gDAA8C;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CACL,EACA,EAAAV,qBAAA,GAAAvB,OAAO,CAAC8C,eAAe,cAAAvB,qBAAA,uBAAvBA,qBAAA,CAAyBwB,MAAM,MAAK,SAAS,iBAC5CnD,OAAA;YAAIgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACpCjC,OAAA;cAAMgC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CrC,OAAA;cAAAiC,QAAA,EAAM;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACL,EACA,CAAC,EAAAT,oBAAA,GAAAxB,OAAO,CAAC2C,UAAU,cAAAnB,oBAAA,uBAAlBA,oBAAA,CAAoBoB,SAAS,KAAI,CAAC,KAAK,EAAE,iBACzChD,OAAA;YAAIgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACpCjC,OAAA;cAAMgC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CrC,OAAA;cAAAiC,QAAA,EAAM;YAAiE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CACL,EACA,CAAC,EAAAR,oBAAA,GAAAzB,OAAO,CAAC2C,UAAU,cAAAlB,oBAAA,uBAAlBA,oBAAA,CAAoBoB,iBAAiB,KAAI,CAAC,IAAI,GAAG,iBACjDjD,OAAA;YAAIgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACpCjC,OAAA;cAAMgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CrC,OAAA;cAAAiC,QAAA,EAAM;YAAwE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CACL,EACA,CAAC,EAAAP,oBAAA,GAAA1B,OAAO,CAAC2C,UAAU,cAAAjB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBsB,oBAAoB,cAAArB,qBAAA,uBAAxCA,qBAAA,CAA0Cc,MAAM,KAAI,CAAC,IAAI,CAAC,iBAC1D7C,OAAA;YAAIgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACpCjC,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CrC,OAAA;cAAAiC,QAAA,GAAM,UAAQ,EAAC7B,OAAO,CAAC2C,UAAU,CAACK,oBAAoB,CAACP,MAAM,EAAC,8BAA4B;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,MAAMgB,gBAAgB,GAAGA,CAAA;IAAA,IAAAC,sBAAA;IAAA,oBACvBtD,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvB,EAAAqB,sBAAA,GAAAlD,OAAO,CAACwC,iBAAiB,cAAAU,sBAAA,uBAAzBA,sBAAA,CAA2BT,MAAM,IAAG,CAAC,gBACpC7C,OAAA,CAAAE,SAAA;QAAA+B,QAAA,gBACEjC,OAAA;UAAKgC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCjC,OAAA;YAAIgC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,GAC5C7B,OAAO,CAACwC,iBAAiB,CAACC,MAAM,EAAC,8BACpC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7B,OAAO,CAACwC,iBAAiB,CAACW,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBAC9CzD,OAAA;YAAiBgC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAC/DjC,OAAA;cAAKgC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjC,OAAA;gBAAMgC,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAC,aAC9B,EAACwB,KAAK,GAAG,CAAC;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACPrC,OAAA;gBAAMgC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,QAC3C,EAACuB,SAAS,CAACE,OAAO,EAAC,OAC3B;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CjC,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAMgC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClDrC,OAAA;kBAAKgC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAErB,UAAU,CAAC4C,SAAS,CAACG,SAAS;gBAAC;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNrC,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAMgC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDrC,OAAA;kBAAKgC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAErB,UAAU,CAAC4C,SAAS,CAACI,OAAO;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNrC,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAMgC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDrC,OAAA;kBAAKgC,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,GAAEuB,SAAS,CAACK,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAtBEoB,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,eACN,CAAC,gBAEHrC,OAAA;QAAKgC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjC,OAAA,CAACL,aAAa;UAACqC,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjErC,OAAA;UAAIgC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvErC,OAAA;UAAGgC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAM0B,oBAAoB,GAAGA,CAAA;IAAA,IAAAC,qBAAA;IAAA,oBAC3BhE,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvB,EAAA+B,qBAAA,GAAA5D,OAAO,CAAC0C,WAAW,cAAAkB,qBAAA,uBAAnBA,qBAAA,CAAqBnB,MAAM,IAAG,CAAC,gBAC9B7C,OAAA,CAAAE,SAAA;QAAA+B,QAAA,gBACEjC,OAAA;UAAKgC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CjC,OAAA;YAAIgC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,GAC/C7B,OAAO,CAAC0C,WAAW,CAACD,MAAM,EAAC,oBAC9B;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGgC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7B,OAAO,CAAC0C,WAAW,CAACS,GAAG,CAAC,CAACU,UAAU,EAAER,KAAK,kBACzCzD,OAAA;YAAiBgC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBAClEjC,OAAA;cAAKgC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjC,OAAA;gBAAMgC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAC,cAChC,EAACwB,KAAK,GAAG,CAAC;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACPrC,OAAA;gBAAMgC,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,GACtElB,IAAI,CAACuB,KAAK,CAAC2B,UAAU,CAACC,eAAe,GAAG,GAAG,CAAC,EAAC,WAChD;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENrC,OAAA;cAAKgC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjC,OAAA;gBAAKgC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCjC,OAAA;kBAAKgC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,QAAM,EAACgC,UAAU,CAACE,KAAK,EAAC,GAAC;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3ErC,OAAA;kBAAKgC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEgC,UAAU,CAACG;gBAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNrC,OAAA;gBAAKgC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCjC,OAAA;kBAAKgC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,QAAM,EAACgC,UAAU,CAACI,KAAK,EAAC,GAAC;gBAAA;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3ErC,OAAA;kBAAKgC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEgC,UAAU,CAACK;gBAAK;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAnBEoB,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,eACN,CAAC,gBAEHrC,OAAA;QAAKgC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjC,OAAA,CAACJ,MAAM;UAACoC,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DrC,OAAA;UAAIgC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzErC,OAAA;UAAGgC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAA8D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrG;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAMkC,gBAAgB,GAAGA,CAAA;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IAAA,oBACvB3F,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjC,OAAA;QAAKgC,SAAS,EAAE,kBACd,EAAAwC,sBAAA,GAAApE,OAAO,CAAC8C,eAAe,cAAAsB,sBAAA,uBAAvBA,sBAAA,CAAyBrB,MAAM,MAAK,SAAS,GAAG,aAAa,GAC7D,EAAAsB,sBAAA,GAAArE,OAAO,CAAC8C,eAAe,cAAAuB,sBAAA,uBAAvBA,sBAAA,CAAyBtB,MAAM,MAAK,WAAW,GAAG,cAAc,GAAG,WAAW,EAC7E;QAAAlB,QAAA,gBACDjC,OAAA;UAAIgC,SAAS,EAAE,sBACb,EAAA0C,sBAAA,GAAAtE,OAAO,CAAC8C,eAAe,cAAAwB,sBAAA,uBAAvBA,sBAAA,CAAyBvB,MAAM,MAAK,SAAS,GAAG,gBAAgB,GAChE,EAAAwB,sBAAA,GAAAvE,OAAO,CAAC8C,eAAe,cAAAyB,sBAAA,uBAAvBA,sBAAA,CAAyBxB,MAAM,MAAK,WAAW,GAAG,iBAAiB,GAAG,cAAc,EACnF;UAAAlB,QAAA,EAAC;QAEJ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrC,OAAA;UAAGgC,SAAS,EAAE,WACZ,EAAA4C,sBAAA,GAAAxE,OAAO,CAAC8C,eAAe,cAAA0B,sBAAA,uBAAvBA,sBAAA,CAAyBzB,MAAM,MAAK,SAAS,GAAG,gBAAgB,GAChE,EAAA0B,sBAAA,GAAAzE,OAAO,CAAC8C,eAAe,cAAA2B,sBAAA,uBAAvBA,sBAAA,CAAyB1B,MAAM,MAAK,WAAW,GAAG,iBAAiB,GAAG,cAAc,EACnF;UAAAlB,QAAA,GACA,EAAA6C,sBAAA,GAAA1E,OAAO,CAAC8C,eAAe,cAAA4B,sBAAA,uBAAvBA,sBAAA,CAAyB3B,MAAM,MAAK,SAAS,IAAI,sEAAsE,EACvH,EAAA4B,sBAAA,GAAA3E,OAAO,CAAC8C,eAAe,cAAA6B,sBAAA,uBAAvBA,sBAAA,CAAyB5B,MAAM,MAAK,WAAW,IAAI,kFAAkF,EACrI,EAAA6B,sBAAA,GAAA5E,OAAO,CAAC8C,eAAe,cAAA8B,sBAAA,uBAAvBA,sBAAA,CAAyB7B,MAAM,MAAK,UAAU,IAAI,yEAAyE;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3H,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrC,OAAA;QAAKgC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDjC,OAAA;UAAKgC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCjC,OAAA;YAAIgC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrErC,OAAA;YAAKgC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7BrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,IAAAgD,sBAAA,GAC1B7E,OAAO,CAAC8C,eAAe,cAAA+B,sBAAA,wBAAAC,uBAAA,GAAvBD,sBAAA,CAAyBW,oBAAoB,cAAAV,uBAAA,uBAA7CA,uBAAA,CAA+CpB,OAAO,CAAC,CAAC,CAAC,EAAC,UAC7D;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpBrC,OAAA;gBAAMgC,SAAS,EAAE,0BACf,EAAAmD,uBAAA,GAAA/E,OAAO,CAAC8C,eAAe,cAAAiC,uBAAA,uBAAvBA,uBAAA,CAAyBhC,MAAM,MAAK,SAAS,GAAG,gBAAgB,GAChE,EAAAiC,uBAAA,GAAAhF,OAAO,CAAC8C,eAAe,cAAAkC,uBAAA,uBAAvBA,uBAAA,CAAyBjC,MAAM,MAAK,WAAW,GAAG,iBAAiB,GAAG,cAAc,EACnF;gBAAAlB,QAAA,GAAAoD,uBAAA,GACAjF,OAAO,CAAC8C,eAAe,cAAAmC,uBAAA,wBAAAC,uBAAA,GAAvBD,uBAAA,CAAyBlC,MAAM,cAAAmC,uBAAA,uBAA/BA,uBAAA,CAAiCO,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACL,EAAAkD,uBAAA,GAAAnF,OAAO,CAAC8C,eAAe,cAAAqC,uBAAA,uBAAvBA,uBAAA,CAAyBO,kBAAkB,IAAG,CAAC,iBAC9C9F,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvBrC,OAAA;gBAAMgC,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GACvC7B,OAAO,CAAC8C,eAAe,CAAC4C,kBAAkB,CAAChC,OAAO,CAAC,CAAC,CAAC,EAAC,UACzD;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCjC,OAAA;YAAIgC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjErC,OAAA;YAAKgC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrBrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,IAAAuD,uBAAA,GAC1BpF,OAAO,CAAC8C,eAAe,cAAAsC,uBAAA,uBAAvBA,uBAAA,CAAyBO,aAAa,EAAC,UAC1C;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrBrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,IAAAwD,uBAAA,GAC1BrF,OAAO,CAAC8C,eAAe,cAAAuC,uBAAA,uBAAvBA,uBAAA,CAAyBO,aAAa,EAAC,UAC1C;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCjC,OAAA;gBAAAiC,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BrC,OAAA;gBAAMgC,SAAS,EAAE,eACf,CAAA0D,uBAAA,GAAAtF,OAAO,CAAC8C,eAAe,cAAAwC,uBAAA,eAAvBA,uBAAA,CAAyBO,gBAAgB,GAAG,gBAAgB,GAAG,cAAc,EAC5E;gBAAAhE,QAAA,EACA,CAAA0D,uBAAA,GAAAvF,OAAO,CAAC8C,eAAe,cAAAyC,uBAAA,eAAvBA,uBAAA,CAAyBM,gBAAgB,GAAG,KAAK,GAAG;cAAI;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;EAED,MAAM6D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMnD,UAAU,GAAG3C,OAAO,CAAC2C,UAAU;IACrC,MAAMC,SAAS,GAAG,CAAAD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,SAAS,KAAI,CAAC;IAC5C,MAAMC,iBAAiB,GAAG,CAAAF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEE,iBAAiB,KAAI,CAAC;IAC5D,MAAMkD,cAAc,GAAG,CAAApD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoD,cAAc,KAAI,CAAC;IACtD,MAAMC,kBAAkB,GAAG,CAAArD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqD,kBAAkB,KAAI,EAAE;IAC/D,MAAMhD,oBAAoB,GAAG,CAAAL,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,oBAAoB,KAAI,EAAE;IACnE,MAAMiD,mBAAmB,GAAG,CAAAtD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEsD,mBAAmB,KAAI;MAAEC,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAE,CAAC;IAC9F,MAAMC,cAAc,GAAG,CAAAzD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEyD,cAAc,KAAI,EAAE;IACvD,MAAMC,mBAAmB,GAAG,CAAA1D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0D,mBAAmB,KAAI;MAAEC,IAAI,EAAE;IAAkB,CAAC;IAC1F,MAAMC,kBAAkB,GAAG,CAAA5D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE4D,kBAAkB,KAAI,CAAC,CAAC;IAE/D,MAAMC,aAAa,GAAIL,KAAK,IAAK;MAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;MACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;MACzC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;MACzC,OAAO,cAAc;IACvB,CAAC;IAED,MAAMM,kBAAkB,GAAIN,KAAK,IAAK;MACpC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,8BAA8B;MACtD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gCAAgC;MACxD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gCAAgC;MACxD,OAAO,0BAA0B;IACnC,CAAC;IAED,MAAMO,aAAa,GAAG,CACpB,kBAAkB,EAAE,uBAAuB,EAAE,gBAAgB,EAC7D,kBAAkB,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,mBAAmB,CACpF;IAED,MAAMC,sBAAsB,GAAGP,cAAc,CAACQ,MAAM,CAACC,OAAO,IAC1DH,aAAa,CAACI,QAAQ,CAACD,OAAO,CAACE,gBAAgB,CACjD,CAAC,CAACtE,MAAM;IAER,MAAMuE,mBAAmB,GAAGZ,cAAc,CAACQ,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACI,UAAU,GAAG,GAAG,CAAC,CAACxE,MAAM;IAC7F,MAAMyE,kBAAkB,GAAGd,cAAc,CAACQ,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACI,UAAU,GAAG,GAAG,CAAC,CAACxE,MAAM;IAE5F,oBACE7C,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBjC,OAAA;QAAKgC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxEjC,OAAA;UAAIgC,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrFrC,OAAA;UAAGgC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAEwE,mBAAmB,CAACC;QAAI;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ErC,OAAA;UAAGgC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAGvC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNrC,OAAA;QAAKgC,SAAS,EAAE,yBAAyB6E,kBAAkB,CAAC7D,SAAS,CAAC,EAAG;QAAAf,QAAA,gBACvEjC,OAAA;UAAKgC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDjC,OAAA;YAAIgC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ErC,OAAA;YAAMgC,SAAS,EAAE,sBAAsB4E,aAAa,CAAC5D,SAAS,CAAC,EAAG;YAAAf,QAAA,GAC/DlB,IAAI,CAACuB,KAAK,CAACU,SAAS,CAAC,EAAC,MACzB;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNrC,OAAA;UAAGgC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,qGAEvC,EAACwE,mBAAmB,CAACC,IAAI,CAACa,WAAW,CAAC,CAAC,EAAC,2CAC3C;QAAA;UAAArF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrC,OAAA;UAAKgC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DjC,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjC,OAAA;cAAKgC,SAAS,EAAE,qBAAqB4E,aAAa,CAAC3D,iBAAiB,GAAG,GAAG,CAAC,EAAG;cAAAhB,QAAA,GAC3ElB,IAAI,CAACuB,KAAK,CAACW,iBAAiB,GAAG,GAAG,CAAC,EAAC,GACvC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjC,OAAA;cAAKgC,SAAS,EAAC,iCAAiC;cAAAC,QAAA,GAC7CkE,cAAc,EAAC,GAAC,EAACC,kBAAkB;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAC/C8E,sBAAsB,EAAC,GAAC,EAACD,aAAa,CAACjE,MAAM;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjC,OAAA;cAAKgC,SAAS,EAAE,qBACdqE,mBAAmB,CAACC,MAAM,KAAK,WAAW,GAAG,gBAAgB,GAC7DD,mBAAmB,CAACC,MAAM,KAAK,MAAM,GAAG,eAAe,GACvDD,mBAAmB,CAACC,MAAM,KAAK,MAAM,GAAG,iBAAiB,GAAG,cAAc,EACzE;cAAArE,QAAA,EACAoE,mBAAmB,CAACC;YAAM;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAKgC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDjC,OAAA;UAAKgC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCjC,OAAA;YAAIgC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxFrC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAC,yBAAuB,EAAC,GAAG,EAAC,MAAI;cAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjErC,OAAA;gBAAMgC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEmF;cAAmB;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjErC,OAAA;gBAAMgC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEuE,cAAc,CAACQ,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACH,UAAU,IAAI,GAAG,IAAIG,CAAC,CAACH,UAAU,IAAI,GAAG,CAAC,CAACxE;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,GAAC,wBAAsB,EAAC,GAAG,EAAC,MAAI;cAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChErC,OAAA;gBAAMgC,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEqF;cAAkB;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DrC,OAAA;gBAAMgC,SAAS,EAAE,eAAe4E,aAAa,CAAC3D,iBAAiB,GAAG,GAAG,CAAC,EAAG;gBAAAhB,QAAA,GACtElB,IAAI,CAACuB,KAAK,CAACW,iBAAiB,GAAG,GAAG,CAAC,EAAC,GACvC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjEjC,OAAA;cAAAiC,QAAA,EAAQ;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6IAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CjC,OAAA;YAAIgC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFrC,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDrC,OAAA;gBAAMgC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAEkE,cAAc,EAAC,GAAC,EAACC,kBAAkB;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDrC,OAAA;gBAAMgC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,GAAElB,IAAI,CAACuB,KAAK,CAACqE,kBAAkB,CAACc,kBAAkB,IAAI,CAAC,CAAC,EAAC,KAAG;cAAA;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDrC,OAAA;gBAAMgC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAElB,IAAI,CAACuB,KAAK,CAACqE,kBAAkB,CAACe,gBAAgB,IAAI,CAAC,CAAC,EAAC,KAAG;cAAA;gBAAAxF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G,CAAC,eACNrC,OAAA;cAAKgC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDjC,OAAA;gBAAMgC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CrC,OAAA;gBAAMgC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAElB,IAAI,CAACuB,KAAK,CAACqE,kBAAkB,CAACgB,eAAe,IAAI,CAAC,CAAC,EAAC,KAAG;cAAA;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLe,oBAAoB,CAACP,MAAM,GAAG,CAAC,iBAC9B7C,OAAA;QAAKgC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DjC,OAAA;UAAIgC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ErC,OAAA;UAAGgC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrC,OAAA;UAAKgC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAClCmB,oBAAoB,CAACG,GAAG,CAAC,CAACqE,IAAI,EAAEnE,KAAK,kBACpCzD,OAAA;YAAkBgC,SAAS,EAAC,6FAA6F;YAAAC,QAAA,EACtH2F,IAAI,CAAC/B,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEgC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;UAAC,GADtDrE,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDrC,OAAA;QAAKgC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCjC,OAAA;UAAIgC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFrC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDjC,OAAA;YAAAiC,QAAA,EAAQ;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,kDACtC,EAACoE,mBAAmB,CAACC,IAAI,CAACa,WAAW,CAAC,CAAC,EAAC,kGAE3C;QAAA;UAAArF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CjC,OAAA;YAAKgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjC,OAAA;cAAMgC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CrC,OAAA;cAAAiC,QAAA,gBAAMjC,OAAA;gBAAAiC,QAAA,EAAQ;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gDAA4C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjC,OAAA;cAAMgC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CrC,OAAA;cAAAiC,QAAA,gBAAMjC,OAAA;gBAAAiC,QAAA,EAAQ;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,mDAA+C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjC,OAAA;cAAMgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CrC,OAAA;cAAAiC,QAAA,gBAAMjC,OAAA;gBAAAiC,QAAA,EAAQ;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,2EAAuE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChI,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjC,OAAA;cAAMgC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CrC,OAAA;cAAAiC,QAAA,gBAAMjC,OAAA;gBAAAiC,QAAA,EAAQ;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kFAA8E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/I,CAAC,eACNrC,OAAA;YAAKgC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjC,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CrC,OAAA;cAAAiC,QAAA,gBAAMjC,OAAA;gBAAAiC,QAAA,EAAQ;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,mDAA+C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDjC,OAAA;YAAAiC,QAAA,EAAQ;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,8LAG/B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrC,OAAA;UAAKgC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDjC,OAAA;YAAAiC,QAAA,EAAQ;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,0QAG7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACErC,OAAA;IAAKgC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBjC,OAAA;MAAIgC,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGnFrC,OAAA;MAAKgC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CjC,OAAA;QAAKgC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BzB,IAAI,CAAC+C,GAAG,CAAEwE,GAAG,IAAK;UACjB,MAAMC,IAAI,GAAGD,GAAG,CAACpH,IAAI;UACrB,oBACEX,OAAA;YAEEiI,OAAO,EAAEA,CAAA,KAAM1H,YAAY,CAACwH,GAAG,CAACtH,EAAE,CAAE;YACpCuB,SAAS,EAAE,oEACT1B,SAAS,KAAKyH,GAAG,CAACtH,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,EAC/E;YAAAwB,QAAA,gBAEHjC,OAAA,CAACgI,IAAI;cAAChG,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC3B0F,GAAG,CAACrH,KAAK;UAAA,GATLqH,GAAG,CAACtH,EAAE;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUL,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAAiC,QAAA,GACG3B,SAAS,KAAK,SAAS,IAAIc,gBAAgB,CAAC,CAAC,EAC7Cd,SAAS,KAAK,SAAS,IAAI+C,gBAAgB,CAAC,CAAC,EAC7C/C,SAAS,KAAK,aAAa,IAAIyD,oBAAoB,CAAC,CAAC,EACrDzD,SAAS,KAAK,SAAS,IAAIiE,gBAAgB,CAAC,CAAC,EAC7CjE,SAAS,KAAK,YAAY,IAAI4F,mBAAmB,CAAC,CAAC;IAAA;MAAAhE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CA3hBIF,eAAe;AAAA+H,EAAA,GAAf/H,eAAe;AA6hBrB,eAAeA,eAAe;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}