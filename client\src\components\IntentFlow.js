import React, { useState } from 'react';
import { MessageSquare, Bot, User, ChevronDown, ChevronUp } from 'lucide-react';

const IntentFlow = ({ intentFlow }) => {
  const [showAll, setShowAll] = useState(false);

  // Handle both old and new data structures
  const intentMappings = intentFlow?.intentMappings || intentFlow || [];
  const displayedFlow = showAll ? intentMappings : intentMappings.slice(0, 10);

  // Enhanced data from new structure
  const flowScore = intentFlow?.flowScore || 0;
  const averageConfidence = intentFlow?.averageConfidence || 0;
  const completedSteps = intentFlow?.completedSteps || 0;
  const totalRequiredSteps = intentFlow?.totalRequiredSteps || 20;
  const missingCriticalSteps = intentFlow?.missingCriticalSteps || [];
  const conversationQuality = intentFlow?.conversationQuality || { rating: 'Unknown', score: 0 };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getStepColor = (step) => {
    const colors = {
      initial_greeting: 'bg-blue-100 text-blue-800',
      identity_verification: 'bg-purple-100 text-purple-800',
      parent_response: 'bg-indigo-100 text-indigo-800',
      class_x_status: 'bg-cyan-100 text-cyan-800',
      marks_percentage: 'bg-teal-100 text-teal-800',
      admission_status: 'bg-green-100 text-green-800',
      institution_type: 'bg-lime-100 text-lime-800',
      school_board_details: 'bg-yellow-100 text-yellow-800',
      stream_selection: 'bg-orange-100 text-orange-800',
      admission_proof: 'bg-red-100 text-red-800',
      dropout_investigation: 'bg-pink-100 text-pink-800',
      summary_confirmation: 'bg-violet-100 text-violet-800',
      closing_statement: 'bg-gray-100 text-gray-800',
      callback_scheduling: 'bg-amber-100 text-amber-800',
      goodbye: 'bg-slate-100 text-slate-800',
      unknown: 'bg-gray-100 text-gray-600'
    };
    return colors[step] || colors.unknown;
  };

  const formatStepName = (step) => {
    return step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Calculate flow statistics
  const botTurns = intentMappings.filter(turn => turn.speaker === 'agent' || turn.speaker === 'bot');
  const humanTurns = intentMappings.filter(turn => turn.speaker === 'customer' || turn.speaker === 'human');
  const avgBotConfidence = botTurns.length > 0 ?
    botTurns.reduce((sum, turn) => sum + turn.confidence, 0) / botTurns.length : 0;
  const avgHumanConfidence = humanTurns.length > 0 ?
    humanTurns.reduce((sum, turn) => sum + turn.confidence, 0) / humanTurns.length : 0;

  const stepCounts = intentMappings.reduce((acc, turn) => {
    acc[turn.conversationStep] = (acc[turn.conversationStep] || 0) + 1;
    return acc;
  }, {});

  return (
    <div className="card">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-purple-100 rounded-lg">
          <MessageSquare className="w-5 h-5 text-purple-600" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-900">Intent Flow Analysis</h3>
          <p className="text-gray-600">Conversation flow and intent detection results</p>
        </div>
      </div>

      {/* Enhanced Flow Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{intentMappings.length}</div>
          <div className="text-sm text-gray-600">Total Turns</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{Math.round(flowScore)}</div>
          <div className="text-sm text-gray-600">Flow Score</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">{Math.round(averageConfidence * 100)}%</div>
          <div className="text-sm text-gray-600">Avg Confidence</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{completedSteps}/{totalRequiredSteps}</div>
          <div className="text-sm text-gray-600">Steps Completed</div>
        </div>
        <div className="text-center">
          <div className={`text-2xl font-bold ${conversationQuality.rating === 'Excellent' ? 'text-green-600' :
                                                conversationQuality.rating === 'Good' ? 'text-blue-600' :
                                                conversationQuality.rating === 'Fair' ? 'text-yellow-600' : 'text-red-600'}`}>
            {conversationQuality.rating}
          </div>
          <div className="text-sm text-gray-600">Quality Rating</div>
        </div>
      </div>

      {/* Missing Critical Steps Alert */}
      {missingCriticalSteps.length > 0 && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-semibold text-red-800 mb-2">⚠️ Missing Critical Steps</h4>
          <div className="flex flex-wrap gap-2">
            {missingCriticalSteps.map((step, index) => (
              <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                {formatStepName(step)}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Conversation Steps Summary */}
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 mb-3">Conversation Steps Covered</h4>
        <div className="flex flex-wrap gap-2">
          {Object.entries(stepCounts).map(([step, count]) => (
            <span
              key={step}
              className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStepColor(step)}`}
            >
              {formatStepName(step)} ({count})
            </span>
          ))}
        </div>
      </div>

      {/* Turn-by-Turn Flow */}
      <div className="space-y-3">
        <h4 className="font-semibold text-gray-900">Turn-by-Turn Analysis</h4>
        
        {displayedFlow.map((turn, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              {/* Speaker Icon */}
              <div className={`p-2 rounded-lg ${(turn.speaker === 'bot' || turn.speaker === 'agent') ? 'bg-blue-100' : 'bg-green-100'}`}>
                {(turn.speaker === 'bot' || turn.speaker === 'agent') ? (
                  <Bot className="w-4 h-4 text-blue-600" />
                ) : (
                  <User className="w-4 h-4 text-green-600" />
                )}
              </div>

              <div className="flex-1">
                {/* Header */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900">
                      Turn #{turn.turnNumber} - {(turn.speaker === 'bot' || turn.speaker === 'agent') ? 'Agent' : 'Human'}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStepColor(turn.conversationStep)}`}>
                      {formatStepName(turn.conversationStep)}
                    </span>
                    {turn.stepNumber && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        Step {turn.stepNumber}
                      </span>
                    )}
                  </div>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(turn.confidence)}`}>
                    {Math.round(turn.confidence * 100)}% confidence
                  </span>
                </div>

                {/* Intent */}
                <div className="mb-2">
                  <span className="text-sm font-medium text-gray-700">Intent: </span>
                  <span className="text-sm text-gray-600">{turn.detectedIntent}</span>
                </div>

                {/* Text */}
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <span className="font-medium text-gray-700">Text: </span>
                  <span className="text-gray-800">{turn.text}</span>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Show More/Less Button */}
        {intentMappings.length > 10 && (
          <div className="text-center">
            <button
              onClick={() => setShowAll(!showAll)}
              className="btn btn-secondary"
            >
              {showAll ? (
                <>
                  <ChevronUp className="w-4 h-4" />
                  Show Less
                </>
              ) : (
                <>
                  <ChevronDown className="w-4 h-4" />
                  Show All ({intentMappings.length - 10} more)
                </>
              )}
            </button>
          </div>
        )}
      </div>

      {/* Confidence Summary */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-3">Confidence Analysis</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-gray-600">Average Bot Confidence</div>
            <div className="text-2xl font-bold text-blue-600">
              {Math.round(avgBotConfidence * 100)}%
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">Average Human Confidence</div>
            <div className="text-2xl font-bold text-green-600">
              {Math.round(avgHumanConfidence * 100)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntentFlow;